#!/bin/bash

echo "تشغيل نظام إدارة المدرسة..."
echo ""

# تشغيل البرنامج باستخدام Python
python run_app.py

# إذا فشل التشغيل، حاول استخدام python3
if [ $? -ne 0 ]; then
    echo "محاولة استخدام python3..."
    python3 run_app.py
fi

# إذا فشلت جميع المحاولات، أظهر رسالة خطأ
if [ $? -ne 0 ]; then
    echo ""
    echo "فشل تشغيل البرنامج. يرجى التأكد من تثبيت Python على جهازك."
    echo "يمكنك تثبيت Python من الموقع الرسمي: https://www.python.org/downloads/"
    echo ""
    read -p "اضغط Enter للخروج..."
fi
