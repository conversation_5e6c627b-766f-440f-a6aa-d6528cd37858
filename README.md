# نظام إدارة المدرسة

## نظرة عامة
نظام متكامل لإدارة المدارس يتضمن إدارة الطلاب، الحضور والغياب، الاستئذان، التأخر، وإرسال الإشعارات لأولياء الأمور عبر واتساب.

## المميزات الرئيسية
- إدارة بيانات الطلاب
- تسجيل الحضور والغياب
- تسجيل حالات التأخر
- إدارة طلبات الاستئذان
- إرسال إشعارات لأولياء الأمور عبر واتساب بشكل تلقائي
- إنشاء تقارير متنوعة
- إدارة المعلمين وحضورهم
- إعدادات المدرسة

## متطلبات التشغيل
- Python 3.7 أو أحدث
- متصفح ويب حديث
- اتصال بالإنترنت (لإرسال رسائل واتساب)
- واتساب ويب مفعل

## طريقة التشغيل
1. تأكد من تثبيت Python على جهازك
2. قم بتثبيت المكتبات المطلوبة باستخدام الأمر:
   ```
   pip install -r requirements.txt
   ```
3. قم بتشغيل البرنامج باستخدام ملف "تشغيل_البرنامج.bat" أو باستخدام الأمر:
   ```
   python app.py
   ```
4. سيتم فتح المتصفح تلقائياً على العنوان: http://localhost:5000

## إرسال الإشعارات عبر واتساب
يدعم النظام إرسال الإشعارات عبر واتساب بشكل تلقائي بالكامل باستخدام ثلاث طرق:

1. **الطريقة المباشرة**: تقوم بإدخال الرسالة مباشرة في واتساب ويب وإرسالها تلقائياً
2. **طريقة Selenium**: تستخدم محاكاة المتصفح لإرسال الرسائل
3. **طريقة pywhatkit**: تستخدم مكتبة pywhatkit كخطة بديلة

للاستفادة من ميزة الإرسال التلقائي:
1. تأكد من تسجيل الدخول إلى واتساب ويب
2. انتقل إلى صفحة الإشعارات
3. اختر المستلمين وأنشئ الإشعار
4. اضغط على زر "إرسال تلقائي بالكامل"

## الدعم الفني
للحصول على المساعدة أو الإبلاغ عن مشكلة، يرجى التواصل مع مطور النظام.

## الإصدار
الإصدار الحالي: 1.0.0
