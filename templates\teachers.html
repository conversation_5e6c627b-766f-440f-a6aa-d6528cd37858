{% extends 'base.html' %}

{% block title %}قائمة المعلمين - نظام إدارة المدرسة{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-3">
    <h2><i class="bi bi-person-badge-fill me-2"></i>قائمة المعلمين</h2>
    <a href="{{ url_for('add_teacher') }}" class="btn btn-success"><i class="bi bi-plus-circle-fill me-1"></i>إضافة معلم جديد</a>
</div>

{% if teachers %}
<table class="table table-striped table-hover">
    <thead>
        <tr>
            <th scope="col">#</th>
            <th scope="col">المعرف</th>
            <th scope="col">الاسم</th>
            <th scope="col">معلومات الاتصال</th>
            <th scope="col">التخصص</th>
            <th scope="col">إجراءات</th>
        </tr>
    </thead>
    <tbody>
        {% for teacher_id, teacher in teachers.items() %}
        <tr>
            <th scope="row">{{ loop.index }}</th>
            <td>{{ teacher_id }}</td>
            <td>{{ teacher.name }}</td>
            <td>{{ teacher.contact }}</td>
            <td>{{ teacher.specialization }}</td>
            <td>
                <a href="{{ url_for('edit_teacher', teacher_id=teacher_id) }}" class="btn btn-sm btn-primary me-1"><i class="bi bi-pencil-fill"></i> تعديل</a>
                <form action="{{ url_for('delete_teacher', teacher_id=teacher_id) }}" method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من رغبتك في حذف هذا المعلم؟ لا يمكن التراجع عن هذا الإجراء.');">
                    <button type="submit" class="btn btn-sm btn-danger"><i class="bi bi-trash-fill"></i> حذف</button>
                </form>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>
{% else %}
<div class="alert alert-info" role="alert">
    لا يوجد معلمون مسجلون حتى الآن. <a href="{{ url_for('add_teacher') }}" class="alert-link">قم بإضافة معلم جديد</a>.
</div>
{% endif %}
{% endblock %}