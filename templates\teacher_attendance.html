{% extends "base.html" %}
{% block title %}تسجيل حضور وغياب المعلمين{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2><i class="bi bi-calendar-check-fill me-2"></i>تسجيل حضور وغياب المعلمين</h2>
    <hr>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="bi bi-calendar-date me-2"></i>تسجيل الحضور ليوم: {{ today_date }}</h5>
        </div>
        <div class="card-body">
            <form method="post" action="{{ url_for('teacher_attendance') }}">
                <div class="row mb-3">
                    <div class="col-md-4">
                        <label for="attendance_date" class="form-label">تاريخ الحضور:</label>
                        <input type="date" class="form-control" id="attendance_date" name="attendance_date" value="{{ today_date }}">
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="button" id="change-date" class="btn btn-outline-primary">تغيير التاريخ</button>
                    </div>
                </div>

                {% if teachers %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover border">
                        <thead class="table-dark">
                            <tr>
                                <th><input type="checkbox" id="select-all-present"> حاضر</th>
                                <th><input type="checkbox" id="select-all-absent"> غائب</th>
                                <th><input type="checkbox" id="select-all-late"> متأخر</th>
                                <th><input type="checkbox" id="select-all-excused"> غائب بعذر</th>
                                <th>اسم المعلم</th>
                                <th>التخصص</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for teacher in teachers %}
                            <tr>
                                <td><input type="radio" name="status_{{ teacher.id }}" value="present" class="form-check-input present-radio" {{ 'checked' if today_attendance.get(teacher.id) == 'present' else '' }}></td>
                                <td><input type="radio" name="status_{{ teacher.id }}" value="absent" class="form-check-input absent-radio" {{ 'checked' if today_attendance.get(teacher.id) == 'absent' else '' }}></td>
                                <td><input type="radio" name="status_{{ teacher.id }}" value="late" class="form-check-input late-radio" {{ 'checked' if today_attendance.get(teacher.id) == 'late' else '' }}></td>
                                <td><input type="radio" name="status_{{ teacher.id }}" value="excused" class="form-check-input excused-radio" {{ 'checked' if today_attendance.get(teacher.id) == 'excused' else '' }}></td>
                                <td>{{ teacher.name }}</td>
                                <td>{{ teacher.specialization }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-3">
                    <button type="submit" class="btn btn-primary"><i class="bi bi-save me-1"></i>حفظ سجل الحضور</button>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="bi bi-info-circle-fill me-2"></i>لا يوجد معلمين مسجلين حتى الآن. <a href="{{ url_for('add_teacher') }}">قم بإضافة معلمين أولاً</a>.
                </div>
                {% endif %}
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    // تغيير التاريخ
    document.getElementById('change-date').addEventListener('click', function() {
        const dateInput = document.getElementById('attendance_date');
        const selectedDate = dateInput.value;
        if (selectedDate) {
            window.location.href = "{{ url_for('teacher_attendance') }}?attendance_date=" + selectedDate;
        }
    });

    // تحديد الكل حاضر
    document.getElementById('select-all-present').addEventListener('change', function() {
        const presentRadios = document.querySelectorAll('.present-radio');
        presentRadios.forEach(radio => {
            radio.checked = this.checked;
        });
    });

    // تحديد الكل غائب
    document.getElementById('select-all-absent').addEventListener('change', function() {
        const absentRadios = document.querySelectorAll('.absent-radio');
        absentRadios.forEach(radio => {
            radio.checked = this.checked;
        });
    });

    // تحديد الكل متأخر
    document.getElementById('select-all-late').addEventListener('change', function() {
        const lateRadios = document.querySelectorAll('.late-radio');
        lateRadios.forEach(radio => {
            radio.checked = this.checked;
        });
    });

    // تحديد الكل غائب بعذر
    document.getElementById('select-all-excused').addEventListener('change', function() {
        const excusedRadios = document.querySelectorAll('.excused-radio');
        excusedRadios.forEach(radio => {
            radio.checked = this.checked;
        });
    });
</script>
{% endblock %}