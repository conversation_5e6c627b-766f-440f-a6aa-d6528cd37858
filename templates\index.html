{% extends 'base.html' %}

{% block title %}الصفحة الرئيسية - نظام إدارة المدرسة{% endblock %}

{% block content %}
<div class="p-5 mb-4 bg-light rounded-3">
    <div class="container-fluid py-5">
        <h1 class="display-5 fw-bold">مرحباً بك في نظام إدارة المدرسة</h1>
        <p class="col-md-8 fs-4">نظام شامل لإدارة بيانات الطلاب والمعلمين والحضور والغياب والتقارير.</p>
        <a href="{{ url_for('list_students') }}" class="btn btn-primary btn-lg me-2" type="button"><i class="bi bi-people-fill me-1"></i>إدارة الطلاب</a>
        <a href="{{ url_for('list_teachers') }}" class="btn btn-secondary btn-lg" type="button"><i class="bi bi-person-badge-fill me-1"></i>إدارة المعلمين</a>
    </div>
</div>

<div class="row align-items-md-stretch">
    <div class="col-md-6 mb-4">
        <div class="h-100 p-5 text-white bg-dark rounded-3">
            <h2>الحضور والغياب</h2>
            <p>تسجيل ومتابعة حضور وغياب الطلاب والمعلمين بسهولة ودقة.</p>
            <a href="{{ url_for('record_student_attendance') }}" class="btn btn-outline-light me-2">سجل حضور الطلاب</a>
            <a href="{{ url_for('teacher_attendance') }}" class="btn btn-outline-light">سجل حضور المعلمين</a>
        </div>
    </div>
    <div class="col-md-6 mb-4">
        <div class="h-100 p-5 bg-light border rounded-3">
            <h2>التقارير</h2>
            <p>عرض تقارير مفصلة حول الحضور والغياب وأداء الطلاب والمعلمين.</p>
            <a href="{{ url_for('student_reports') }}" class="btn btn-outline-secondary me-2">تقارير الطلاب</a>
            <a href="{{ url_for('teacher_reports') }}" class="btn btn-outline-secondary">تقارير المعلمين</a>
        </div>
    </div>
</div>
{% endblock %}