"""
برنامج بسيط لتشغيل تطبيق نظام إدارة المدرسة
"""

import os
import sys
import subprocess
import webbrowser
import time

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    print("=" * 50)
    print("تشغيل نظام إدارة المدرسة")
    print("=" * 50)
    print("")
    
    # تشغيل التطبيق
    print("جاري تشغيل التطبيق...")
    
    # تحديد أمر التشغيل المناسب
    python_cmd = "python"
    if sys.platform == "win32":
        # محاولة استخدام python أولاً
        try:
            subprocess.check_call([python_cmd, "--version"], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        except:
            # إذا فشل، جرب python3
            python_cmd = "python3"
            try:
                subprocess.check_call([python_cmd, "--version"], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            except:
                print("خطأ: لم يتم العثور على Python. يرجى تثبيت Python من الموقع الرسمي:")
                print("https://www.python.org/downloads/")
                input("اضغط أي مفتاح للخروج...")
                return
    
    # تشغيل التطبيق
    try:
        # تشغيل التطبيق في عملية منفصلة
        if sys.platform == "win32":  # Windows
            process = subprocess.Popen([python_cmd, "app.py"], creationflags=subprocess.CREATE_NEW_CONSOLE)
        else:  # Linux/Mac
            process = subprocess.Popen([python_cmd, "app.py"])
        
        print("تم تشغيل التطبيق بنجاح.")
        
        # انتظار 5 ثوانٍ ثم فتح المتصفح
        print("انتظر 5 ثوانٍ حتى يتم تشغيل الخادم...")
        time.sleep(5)
        
        # فتح المتصفح
        print("جاري فتح المتصفح...")
        webbrowser.open("http://127.0.0.1:5000")
        
        print("")
        print("=" * 50)
        print("ملاحظات هامة:")
        print("1. إذا لم يفتح المتصفح تلقائياً، يرجى فتح المتصفح يدوياً والانتقال إلى:")
        print("   http://127.0.0.1:5000")
        print("2. لإيقاف البرنامج، أغلق نافذة موجه الأوامر التي تعرض رسائل الخادم.")
        print("=" * 50)
        
        # الانتظار حتى انتهاء العملية
        process.wait()
        
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")
        input("اضغط أي مفتاح للخروج...")

if __name__ == "__main__":
    main()
