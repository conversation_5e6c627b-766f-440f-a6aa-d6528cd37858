@echo off
echo تشغيل نظام إدارة المدرسة...
echo.
echo يرجى الانتظار حتى يتم تشغيل البرنامج...
echo.

REM التأكد من وجود Python
python --version > nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Python غير مثبت على جهازك.
    echo يرجى تثبيت Python من الموقع الرسمي: https://www.python.org/downloads/
    echo.
    pause
    exit /b
)

REM التأكد من تثبيت المكتبات المطلوبة
echo جاري التحقق من المكتبات المطلوبة...
pip install -r requirements.txt

REM تشغيل البرنامج في نافذة منفصلة
start cmd /k "python app.py"

REM انتظار 5 ثوانٍ ثم فتح المتصفح
echo انتظر 5 ثوانٍ حتى يتم تشغيل الخادم...
timeout /t 5 /nobreak > nul

REM فتح المتصفح
echo جاري فتح المتصفح...
start http://127.0.0.1:5000

echo.
echo تم تشغيل البرنامج بنجاح!
echo.
echo ملاحظات هامة:
echo 1. إذا لم يفتح المتصفح تلقائياً، يرجى فتح المتصفح يدوياً والانتقال إلى:
echo    http://127.0.0.1:5000
echo 2. لإيقاف البرنامج، أغلق نافذة موجه الأوامر التي تعرض رسائل الخادم.
echo.
echo اضغط أي مفتاح للخروج من هذه النافذة...
pause
