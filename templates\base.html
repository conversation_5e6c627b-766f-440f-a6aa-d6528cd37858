
<!doctype html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{% block title %}نظام إدارة المدرسة{% endblock %}</title>
    <!-- Add Bootstrap CSS for styling and responsiveness -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/custom.css') }}" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        :root {
            --sidebar-width: 280px;
            --header-height: 160px; /* Increased height for logo and text */
            --primary-color: #0d6efd; /* Bootstrap primary blue */
            --sidebar-bg: #343a40; /* Dark background for sidebar */
            --sidebar-link-color: #adb5bd;
            --sidebar-link-hover-color: #ffffff;
            --sidebar-link-active-color: #ffffff;
            --sidebar-link-active-bg: var(--primary-color);
        }

        body {
            font-family: "Times New Roman", serif;
            background-color: #f8f9fa;
            display: flex;
            min-height: 100vh;
            overflow-x: hidden; /* Prevent horizontal scroll */
        }

        /* Sidebar Styles */
        .sidebar {
            width: var(--sidebar-width);
            min-height: 100vh;
            background-color: var(--sidebar-bg);
            padding-top: calc(var(--header-height) + 10px); /* Space for fixed header with extra padding */
            position: fixed;
            top: 0;
            right: 0; /* Position sidebar on the right for RTL */
            bottom: 0;
            z-index: 1031; /* Ensure sidebar is above top-banner */
            transition: margin-right 0.3s ease; /* Smooth transition for potential toggle */
            overflow-y: auto; /* Allow scrolling within sidebar */
        }

        .sidebar .nav-link {
            color: var(--sidebar-link-color);
            padding: 0.75rem 1.5rem;
            display: flex;
            align-items: center;
            transition: background-color 0.2s ease, color 0.2s ease;
            white-space: nowrap;
        }

        .sidebar .nav-link i {
            margin-left: 0.75rem; /* Space between icon and text in RTL */
            font-size: 1.1rem;
            width: 20px; /* Fixed width for alignment */
            text-align: center;
        }

        .sidebar .nav-link:hover {
            color: var(--sidebar-link-hover-color);
            background-color: rgba(255, 255, 255, 0.1);
        }

        .sidebar .nav-link.active {
            color: var(--sidebar-link-active-color);
            background-color: var(--sidebar-link-active-bg);
            font-weight: 500;
        }

        .sidebar .dropdown-toggle::after {
            margin-right: auto; /* Push dropdown arrow to the left in RTL */
            margin-left: 0;
        }

        .sidebar .dropdown-menu {
            background-color: var(--sidebar-bg);
            border: none;
            padding: 0;
            margin: 0;
        }

        .sidebar .dropdown-item {
            color: var(--sidebar-link-color);
            padding: 0.5rem 1.5rem 0.5rem 1.5rem; /* Adjusted base padding */
            transition: background-color 0.2s ease, color 0.2s ease;
            white-space: nowrap;
        }

        /* Specific padding for items within collapsed submenus */
        .sidebar .collapse .dropdown-item {
            padding-right: 3rem; /* Increase right indent for submenu items in RTL */
        }

        .sidebar .dropdown-item:hover,
        .sidebar .dropdown-item:focus {
            color: var(--sidebar-link-hover-color);
            background-color: rgba(255, 255, 255, 0.1);
        }

        /* Style for highlighting specific items */
        .highlight-red-border {
            border: 2px solid red !important;
            background-color: lightpink !important;
            color: black !important;
            display: block !important;
            padding: 5px !important;
        }

        .sidebar-header {
            padding: 0.5rem; /* Adjust padding */
            color: #ffffff;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: absolute; /* Keep header visible */
            top: 0;
            right: 0;
            width: var(--sidebar-width); /* Explicitly set width */
            height: auto; /* Allow height to adjust to content */
            min-height: var(--header-height);
            background-color: var(--sidebar-bg);
            z-index: 1032; /* Above sidebar and top-banner */
            box-sizing: border-box; /* Ensure padding is included in width/height */
        }

        .sidebar-header img {
            margin-left: 0.5rem;
        }

        /* Header/Banner Styles */
        .top-banner {
            height: 60px; /* Keep original height for top banner */
            background-color: #ffffff;
            box-shadow: 0 2px 4px rgba(0,0,0,.1);
            position: fixed;
            top: 0;
            right: var(--sidebar-width); /* Position to the left of the sidebar */
            left: 0; /* Extend to the left edge */
            z-index: 1030; /* Above sidebar */
            display: flex;
            align-items: center;
            padding: 0 1.5rem; /* Reset padding */
        }

        /* Main Content Area */
        .main-content {
            margin-right: var(--sidebar-width); /* Space for the fixed sidebar */
            margin-top: 60px; /* Space for the fixed header - keep original height */
            padding: 2rem; /* Combined padding */
            width: calc(100% - var(--sidebar-width));
            flex-grow: 1;
        }

        .content-container {
            background-color: #ffffff;
            padding: 2rem;
            border-radius: 0.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,.05);
            margin-top: 1.5rem;
        }

        .flash-messages .alert {
            margin-top: 0; /* Remove extra top margin */
            margin-bottom: 1.5rem;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .sidebar {
                margin-right: calc(-1 * var(--sidebar-width)); /* Hide sidebar off-screen */
                /* Add class toggling via JS later if needed for button */
            }
            .main-content {
                margin-right: 0;
                width: 100%;
            }
            .top-banner {
                right: 0; /* Full width header on small screens */
            }
            /* Add a button to toggle sidebar if needed */
            .sidebar-toggle-btn {
                 display: block; /* Example */
                 position: fixed;
                 top: 15px;
                 right: 15px; /* Adjust position */
                 z-index: 1031;
            }
        }

    </style>
</head>
<body>

<!-- Sidebar -->
<div class="d-flex flex-column flex-shrink-0 text-white bg-dark sidebar">
    <div class="sidebar-header">
        <a href="{{ url_for('index') }}" class="text-white text-decoration-none" style="height: 100%;">
            <div class="text-center mb-3 mt-2">
                {% if settings.logo_path %}
                    <img src="{{ url_for('static', filename=settings.logo_path) }}" alt="شعار" width="80" height="80" class="rounded-circle mb-2">
                {% else %}
                    <i class="bi bi-building" style="font-size: 3rem;"></i>
                {% endif %}
                <div class="text-center">
                    <div class="fs-6 fw-bold">{{ settings.education_department }}</div>
                    <div class="fs-5 fw-bold">{{ settings.school_name }}</div>
                </div>
            </div>
        </a>
    </div>
    <hr class="mb-3" style="border-top: 1px solid rgba(255, 255, 255, 0.1); margin-top: 0;">
    <ul class="nav nav-pills flex-column mb-auto" style="padding-top: 0.5rem;">
        <li class="nav-item">
            <a href="{{ url_for('index') }}" class="nav-link {{ 'active' if request.endpoint == 'index' else '' }}" aria-current="page">
                <i class="bi bi-house-door-fill"></i>
                الرئيسية
            </a>
        </li>
        <li>
            <a href="#studentSubmenu" data-bs-toggle="collapse" class="nav-link dropdown-toggle {{ 'active' if request.endpoint.startswith('list_students') or request.endpoint.startswith('add_student') or request.endpoint.startswith('record_student_attendance') or request.endpoint == 'student_permission' or request.endpoint.startswith('student_reports') or request.endpoint.startswith('behavioral_violations') else '' }}">
                <i class="bi bi-people-fill"></i>
                الطلاب
            </a>
            <ul class="collapse list-unstyled {{ 'show' if request.endpoint.startswith('list_students') or request.endpoint.startswith('add_student') or request.endpoint.startswith('record_student_attendance') or request.endpoint == 'student_permission' or request.endpoint.startswith('student_reports') or request.endpoint.startswith('behavioral_violations') else '' }}" id="studentSubmenu">
                <li><a class="dropdown-item {{ 'active' if request.endpoint == 'list_students' else '' }}" href="{{ url_for('list_students') }}">عرض الطلاب</a></li>
                <li><a class="dropdown-item {{ 'active' if request.endpoint == 'add_student' else '' }}" href="{{ url_for('add_student') }}">إضافة طالب</a></li>
                <li><a class="dropdown-item {{ 'active' if request.endpoint == 'record_student_attendance' else '' }}" href="{{ url_for('record_student_attendance') }}" style="white-space: normal; line-height: 1.2;">تسجيل الحضور والانصراف</a></li>
                <li><a class="dropdown-item {{ 'active' if request.endpoint == 'student_permission' else '' }}" href="{{ url_for('student_permission') }}">استئذان الطلاب</a></li>
                <li><a class="dropdown-item {{ 'active' if request.endpoint == 'student_reports' else '' }}" href="{{ url_for('student_reports') }}">تقارير الحضور</a></li>
                <li><a class="dropdown-item {{ 'active' if request.endpoint == 'behavioral_violations' else '' }}" href="{{ url_for('behavioral_violations') }}">المخالفات السلوكية</a></li> <!-- Added behavioral violations link -->
            </ul>
        </li>
        <li>
            <a href="#teacherSubmenu" data-bs-toggle="collapse" aria-expanded="{{ 'true' if request.endpoint.startswith('list_teachers') or request.endpoint.startswith('add_teacher') or request.endpoint == 'teacher_attendance' else 'false' }}" class="nav-link dropdown-toggle {{ 'active' if request.endpoint.startswith('list_teachers') or request.endpoint.startswith('add_teacher') or request.endpoint == 'teacher_attendance' else '' }}">
                <i class="bi bi-person-badge-fill"></i>
                المعلمين
            </a>
            <ul class="collapse list-unstyled {{ 'show' if request.endpoint.startswith('list_teachers') or request.endpoint.startswith('add_teacher') or request.endpoint == 'teacher_attendance' else '' }}" id="teacherSubmenu">
                <li><a class="dropdown-item {{ 'active' if request.endpoint == 'list_teachers' else '' }}" href="{{ url_for('list_teachers') }}">عرض المعلمين</a></li>
                <li><a class="dropdown-item {{ 'active' if request.endpoint == 'add_teacher' else '' }}" href="{{ url_for('add_teacher') }}">إضافة معلم</a></li>
                <li><hr class="dropdown-divider" style="border-top: 1px solid rgba(255, 255, 255, 0.1);"></li>
                <li><a class="dropdown-item {{ 'active' if request.endpoint == 'teacher_attendance' else '' }}" href="{{ url_for('teacher_attendance') }}">تسجيل الغياب/التأخر</a></li>
                <li><a class="dropdown-item {{ 'active' if request.endpoint == 'teacher_reports' else '' }}" href="{{ url_for('teacher_reports') }}">تقارير المعلمين</a></li>
            </ul>
        </li>
        <li>
            <a href="{{ url_for('notifications') }}" class="nav-link {{ 'active' if request.endpoint == 'notifications' else '' }}">
                <i class="bi bi-bell-fill"></i>
                إرسال الإشعارات
            </a>
        </li>
        <li>
            <a href="{{ url_for('settings') }}" class="nav-link {{ 'active' if request.endpoint == 'settings' else '' }}">
                <i class="bi bi-gear-fill"></i>
                الإعدادات
            </a>
        </li>
    </ul>
    <hr style="border-top: 1px solid rgba(255, 255, 255, 0.1);">
    <div class="dropdown p-3">
        <a href="#" class="d-flex align-items-center text-white text-decoration-none dropdown-toggle" id="dropdownUser1" data-bs-toggle="dropdown" aria-expanded="false">
            <img src="#" alt="" width="32" height="32" class="rounded-circle me-2">
            <strong>المستخدم</strong> <!-- Placeholder for username -->
        </a>
        <ul class="dropdown-menu dropdown-menu-dark text-small shadow" aria-labelledby="dropdownUser1">
            <li><a class="dropdown-item" href="#">ملف شخصي</a></li>
            <li><hr class="dropdown-divider"></li>
            <li><a class="dropdown-item" href="#">تسجيل الخروج</a></li>
        </ul>
    </div>
</div>

<!-- Top Banner / Header -->
<header class="top-banner">
    <!-- Can add breadcrumbs, search bar, or other header elements here -->
    <span class="text-muted">مرحباً بك في نظام إدارة المدرسة</span>
    <!-- Add a sidebar toggle button for smaller screens if needed -->
     <button class="btn btn-outline-secondary d-md-none sidebar-toggle-btn" type="button" data-bs-toggle="offcanvas" data-bs-target="#sidebarMenu" aria-controls="sidebarMenu">
        <i class="bi bi-list"></i>
    </button>
</header>

<!-- Main Content Area -->
<div class="main-content">
    <div class="content-container">
        <div class="flash-messages">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category if category != 'error' else 'danger' }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
        </div>
        {% block content %}{% endblock %}
    </div>
</div>

<!-- Add Bootstrap JS Bundle -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
{% block scripts %}{% endblock %}
</body>
</html>