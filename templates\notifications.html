{% extends 'base.html' %}

{% block title %}إرسال الإشعارات - نظام إدارة المدرسة{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-3">
    <h2><i class="bi bi-bell-fill me-2"></i>إرسال الإشعارات</h2>
    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addNotificationModal">
            <i class="bi bi-plus-circle-fill me-1"></i>إنشاء إشعار جديد
        </button>
    </div>
</div>

<!-- نموذج البحث والتصفية -->
<form method="GET" action="{{ url_for('notifications') }}" class="mb-4 p-3 border rounded bg-light">
    <div class="row g-3">
        <div class="col-md-4">
            <label for="search_query" class="form-label"><i class="bi bi-search me-1"></i>بحث (بالاسم أو المعرف):</label>
            <input type="text" class="form-control" id="search_query" name="search_query" value="{{ request.args.get('search_query', '') }}">
        </div>
        <div class="col-md-3">
            <label for="filter_date" class="form-label"><i class="bi bi-calendar-date me-1"></i>تصفية حسب التاريخ:</label>
            <input type="date" class="form-control" id="filter_date" name="filter_date" value="{{ request.args.get('filter_date', '') }}">
        </div>
        <div class="col-md-3">
            <label for="filter_type" class="form-label"><i class="bi bi-funnel me-1"></i>تصفية حسب النوع:</label>
            <select class="form-select" id="filter_type" name="filter_type">
                <option value="">الكل</option>
                <option value="absences" {% if request.args.get('filter_type') == 'absences' %}selected{% endif %}>الغيابات</option>
                <option value="permissions" {% if request.args.get('filter_type') == 'permissions' %}selected{% endif %}>الاستئذان</option>
                <option value="tardiness" {% if request.args.get('filter_type') == 'tardiness' %}selected{% endif %}>التأخر</option>
            </select>
        </div>
        <div class="col-md-2 d-flex align-items-end">
            <button type="submit" class="btn btn-primary w-100"><i class="bi bi-search me-1"></i>بحث / تصفية</button>
        </div>
    </div>
</form>

<!-- التبويبات -->
<ul class="nav nav-tabs mb-4" id="notificationTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="records-tab" data-bs-toggle="tab" data-bs-target="#records-content" type="button" role="tab" aria-controls="records-content" aria-selected="true">
            <i class="bi bi-list-check me-1"></i>سجلات الغياب والاستئذان والتأخر
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="notifications-tab" data-bs-toggle="tab" data-bs-target="#notifications-content" type="button" role="tab" aria-controls="notifications-content" aria-selected="false">
            <i class="bi bi-bell me-1"></i>الإشعارات المرسلة
        </button>
    </li>
</ul>

<!-- محتوى التبويبات -->
<div class="tab-content" id="notificationTabsContent">
    <!-- تبويب سجلات الغياب والاستئذان والتأخر -->
    <div class="tab-pane fade show active" id="records-content" role="tabpanel" aria-labelledby="records-tab">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th scope="col">#</th>
                        <th scope="col">اسم الطالب</th>
                        <th scope="col">المعرف</th>
                        <th scope="col">الصف/الفصل</th>
                        <th scope="col">التاريخ</th>
                        <th scope="col">النوع</th>
                        <th scope="col">التفاصيل</th>
                        <th scope="col">إجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% if records %}
                        {% for record in records %}
                        <tr>
                            <th scope="row">{{ loop.index }}</th>
                            <td>{{ record.student_name }}</td>
                            <td>{{ record.student_id }}</td>
                            <td>{{ record.grade }}/{{ record.classroom }}</td>
                            <td>{{ record.date }}</td>
                            <td>
                                {% if record.type == 'absence' %}
                                    <span class="badge bg-danger">غياب</span>
                                {% elif record.type == 'permission' %}
                                    <span class="badge bg-warning text-dark">استئذان</span>
                                {% elif record.type == 'tardiness' %}
                                    <span class="badge bg-info">تأخر</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if record.type == 'absence' %}
                                    غياب كامل اليوم
                                {% elif record.type == 'permission' %}
                                    خروج: {{ record.exit_time }} -
                                    {% if record.return_time and record.return_time != 'لم يعد بعد' %}
                                        عودة: {{ record.return_time }}
                                    {% else %}
                                        لم يعد بعد
                                    {% endif %}
                                    <br>
                                    <small>{{ record.reason }}</small>
                                {% elif record.type == 'tardiness' %}
                                    تأخر {{ record.minutes_late }} دقيقة
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-success" onclick="sendNotification('{{ record.id }}', '{{ record.type }}', '{{ record.student_id }}', '{{ record.student_name }}')">
                                        <i class="bi bi-send"></i> إرسال إشعار
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="8" class="text-center">لا توجد سجلات مطابقة للبحث</td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- تبويب الإشعارات المرسلة -->
    <div class="tab-pane fade" id="notifications-content" role="tabpanel" aria-labelledby="notifications-tab">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th scope="col">#</th>
                        <th scope="col">العنوان</th>
                        <th scope="col">النوع</th>
                        <th scope="col">تاريخ الإرسال</th>
                        <th scope="col">عدد المستلمين</th>
                        <th scope="col">الحالة</th>
                        <th scope="col">إجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% if notifications %}
                        {% for notification in notifications %}
                        <tr>
                            <th scope="row">{{ loop.index }}</th>
                            <td>{{ notification.title }}</td>
                            <td>
                                {% if notification.type == 'all' %}
                                    <span class="badge bg-primary">إشعار عام</span>
                                {% elif notification.type == 'students' %}
                                    <span class="badge bg-success">إشعار للطلاب</span>
                                {% elif notification.type == 'teachers' %}
                                    <span class="badge bg-info">إشعار للمعلمين</span>
                                {% elif notification.type == 'parents' %}
                                    <span class="badge bg-warning text-dark">إشعار لأولياء الأمور</span>
                                {% endif %}
                            </td>
                            <td>{{ notification.date }}</td>
                            <td>{{ notification.recipients_count }}</td>
                            <td>
                                {% if notification.status == 'sent' %}
                                    <span class="badge bg-success">تم الإرسال</span>
                                {% elif notification.status == 'scheduled' %}
                                    <span class="badge bg-warning text-dark">مجدول</span>
                                {% elif notification.status == 'draft' %}
                                    <span class="badge bg-secondary">مسودة</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#viewNotificationModal" onclick="viewNotification('{{ notification.id }}')">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-warning" data-bs-toggle="modal" data-bs-target="#editNotificationModal" onclick="editNotification('{{ notification.id }}')">
                                        <i class="bi bi-pencil-square"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-success" onclick="resendNotification('{{ notification.id }}')">
                                        <i class="bi bi-send"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-danger" onclick="deleteNotification('{{ notification.id }}')">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="7" class="text-center">لا توجد إشعارات مسجلة</td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal إضافة إشعار جديد -->
<div class="modal fade" id="addNotificationModal" tabindex="-1" aria-labelledby="addNotificationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="addNotificationModalLabel"><i class="bi bi-plus-circle me-2"></i>إنشاء إشعار جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addNotificationForm" method="POST" action="{{ url_for('notifications') }}">
                    <div class="mb-3">
                        <label for="notification_title" class="form-label">عنوان الإشعار:</label>
                        <input type="text" class="form-control" id="notification_title" name="notification_title" required>
                    </div>
                    <div class="mb-3">
                        <label for="notification_content" class="form-label">محتوى الإشعار:</label>
                        <textarea class="form-control" id="notification_content" name="notification_content" rows="5" required></textarea>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="notification_type" class="form-label">نوع الإشعار:</label>
                            <select class="form-select" id="notification_type" name="notification_type" required>
                                <option value="" disabled selected>-- اختر نوع الإشعار --</option>
                                <option value="all">إشعار عام</option>
                                <option value="students">إشعار للطلاب</option>
                                <option value="teachers">إشعار للمعلمين</option>
                                <option value="parents">إشعار لأولياء الأمور</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="notification_date" class="form-label">تاريخ الإرسال:</label>
                            <input type="date" class="form-control" id="notification_date" name="notification_date" required value="{{ today_date }}">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="notification_time" class="form-label">وقت الإرسال (اختياري):</label>
                            <input type="time" class="form-control" id="notification_time" name="notification_time">
                            <div class="form-text">إذا تركت هذا الحقل فارغًا، سيتم إرسال الإشعار فورًا</div>
                        </div>
                        <div class="col-md-6">
                            <label for="notification_priority" class="form-label">أولوية الإشعار:</label>
                            <select class="form-select" id="notification_priority" name="notification_priority" required>
                                <option value="normal" selected>عادية</option>
                                <option value="high">عالية</option>
                                <option value="urgent">عاجلة</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3" id="recipients_container" style="display: none;">
                        <label for="notification_recipients" class="form-label">المستلمون:</label>
                        <select class="form-select" id="notification_recipients" name="notification_recipients" multiple>
                            <!-- سيتم ملء هذا الحقل ديناميكيًا بناءً على نوع الإشعار -->
                        </select>
                        <div class="form-text">يمكنك اختيار مستلمين محددين أو ترك الحقل فارغًا لإرسال الإشعار لجميع المستلمين من النوع المحدد</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">طرق إرسال الإشعار:</label>
                        <div class="d-flex flex-wrap gap-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="send_sms" name="send_sms">
                                <label class="form-check-label" for="send_sms">
                                    <i class="bi bi-chat-dots me-1"></i>رسالة نصية SMS
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="send_whatsapp" name="send_whatsapp">
                                <label class="form-check-label" for="send_whatsapp">
                                    <i class="bi bi-whatsapp me-1" style="color: #25D366;"></i>واتساب
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="send_email" name="send_email">
                                <label class="form-check-label" for="send_email">
                                    <i class="bi bi-envelope me-1"></i>بريد إلكتروني
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="send_app" name="send_app" checked>
                                <label class="form-check-label" for="send_app">
                                    <i class="bi bi-app-indicator me-1"></i>داخل التطبيق
                                </label>
                            </div>
                        </div>
                    </div>


                </form>
            </div>
            <div class="modal-footer">
                <div class="row w-100">
                    <div class="col-md-6 text-start">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><i class="bi bi-x-circle me-1"></i>إلغاء</button>
                        <button type="button" class="btn btn-success" id="sendNowBtn"><i class="bi bi-send me-1"></i>إرسال الإشعار</button>
                    </div>
                    <div class="col-md-6 text-end">
                        <button type="button" class="btn btn-outline-secondary" id="saveAsDraftBtn"><i class="bi bi-save me-1"></i>حفظ كمسودة</button>
                        <button type="button" class="btn btn-outline-primary" id="scheduleBtn"><i class="bi bi-clock me-1"></i>جدولة الإرسال</button>
                        <input type="hidden" name="action" id="notificationAction" value="send" form="addNotificationForm">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal عرض الإشعار -->
<div class="modal fade" id="viewNotificationModal" tabindex="-1" aria-labelledby="viewNotificationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="viewNotificationModalLabel"><i class="bi bi-eye me-2"></i>عرض الإشعار</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="card mb-3">
                    <div class="card-header" id="view_notification_title">عنوان الإشعار</div>
                    <div class="card-body">
                        <p class="card-text" id="view_notification_content">محتوى الإشعار</p>
                    </div>
                    <div class="card-footer text-muted">
                        <div class="row">
                            <div class="col-md-6" id="view_notification_date">التاريخ: </div>
                            <div class="col-md-6 text-end" id="view_notification_type">النوع: </div>
                        </div>
                    </div>
                </div>
                <div class="alert alert-info" id="view_notification_stats">
                    تم إرسال الإشعار إلى 0 مستلم
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-success" id="viewResendBtn"><i class="bi bi-send me-1"></i>إعادة الإرسال</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تغيير المستلمين بناءً على نوع الإشعار
        const notificationType = document.getElementById('notification_type');
        const recipientsContainer = document.getElementById('recipients_container');
        const recipientsSelect = document.getElementById('notification_recipients');
        const sendWhatsappCheckbox = document.getElementById('send_whatsapp');
        const whatsappInfoAlert = document.getElementById('whatsapp_info');
        const openWhatsappWebBtn = document.getElementById('open_whatsapp_web');

        // أزرار الإرسال والحفظ والجدولة
        const saveAsDraftBtn = document.getElementById('saveAsDraftBtn');
        const scheduleBtn = document.getElementById('scheduleBtn');
        const sendNowBtn = document.getElementById('sendNowBtn');
        const notificationAction = document.getElementById('notificationAction');
        const notificationForm = document.getElementById('addNotificationForm');
        const notificationTimeInput = document.getElementById('notification_time');



        // معالجة زر حفظ كمسودة
        if (saveAsDraftBtn) {
            saveAsDraftBtn.addEventListener('click', function() {
                // التحقق من إدخال العنوان والمحتوى على الأقل
                const titleInput = document.getElementById('notification_title');
                const contentInput = document.getElementById('notification_content');

                if (!titleInput.value.trim()) {
                    alert('يرجى إدخال عنوان الإشعار');
                    titleInput.focus();
                    return;
                }

                if (!contentInput.value.trim()) {
                    alert('يرجى إدخال محتوى الإشعار');
                    contentInput.focus();
                    return;
                }

                // تعيين الإجراء إلى "draft" وإرسال النموذج
                notificationAction.value = 'draft';
                notificationForm.submit();
            });
        }

        // معالجة زر جدولة الإرسال
        if (scheduleBtn) {
            scheduleBtn.addEventListener('click', function() {
                // التحقق من إدخال العنوان والمحتوى والتاريخ والوقت
                const titleInput = document.getElementById('notification_title');
                const contentInput = document.getElementById('notification_content');
                const dateInput = document.getElementById('notification_date');

                if (!titleInput.value.trim()) {
                    alert('يرجى إدخال عنوان الإشعار');
                    titleInput.focus();
                    return;
                }

                if (!contentInput.value.trim()) {
                    alert('يرجى إدخال محتوى الإشعار');
                    contentInput.focus();
                    return;
                }

                if (!dateInput.value) {
                    alert('يرجى تحديد تاريخ الإرسال');
                    dateInput.focus();
                    return;
                }

                if (!notificationTimeInput.value) {
                    alert('يرجى تحديد وقت الإرسال للجدولة');
                    notificationTimeInput.focus();
                    return;
                }

                // التحقق من أن التاريخ والوقت في المستقبل
                const scheduledDateTime = new Date(`${dateInput.value}T${notificationTimeInput.value}`);
                const now = new Date();

                if (scheduledDateTime <= now) {
                    alert('يجب أن يكون وقت الجدولة في المستقبل');
                    return;
                }

                // تعيين الإجراء إلى "schedule" وإرسال النموذج
                notificationAction.value = 'schedule';
                notificationForm.submit();
            });
        }

        // معالجة زر الإرسال الآن
        if (sendNowBtn) {
            sendNowBtn.addEventListener('click', function(event) {
                // منع السلوك الافتراضي للزر
                event.preventDefault();

                // التحقق من إدخال العنوان والمحتوى
                const titleInput = document.getElementById('notification_title');
                const contentInput = document.getElementById('notification_content');
                const typeSelect = document.getElementById('notification_type');
                const dateInput = document.getElementById('notification_date');

                if (!titleInput.value.trim()) {
                    alert('يرجى إدخال عنوان الإشعار');
                    titleInput.focus();
                    return;
                }

                if (!contentInput.value.trim()) {
                    alert('يرجى إدخال محتوى الإشعار');
                    contentInput.focus();
                    return;
                }

                if (!typeSelect.value) {
                    alert('يرجى اختيار نوع الإشعار');
                    typeSelect.focus();
                    return;
                }

                if (!dateInput.value) {
                    alert('يرجى تحديد تاريخ الإرسال');
                    dateInput.focus();
                    return;
                }

                // التحقق من اختيار مستلم واحد على الأقل إذا كان النوع ليس "إشعار عام"
                if (typeSelect.value !== 'all') {
                    const recipientsSelect = document.getElementById('notification_recipients');
                    if (recipientsSelect && recipientsSelect.selectedOptions.length === 0) {
                        alert('يرجى اختيار مستلم واحد على الأقل');
                        recipientsSelect.focus();
                        return;
                    }
                }

                // تعيين الإجراء إلى "send" وإرسال النموذج
                notificationAction.value = 'send';
                console.log('إرسال النموذج...');
                notificationForm.submit();
            });
        }

        if (notificationType) {
            notificationType.addEventListener('change', function() {
                const selectedType = this.value;

                // إظهار/إخفاء حقل المستلمين
                if (selectedType && selectedType !== 'all') {
                    recipientsContainer.style.display = 'block';
                    loadRecipients(selectedType);
                } else {
                    recipientsContainer.style.display = 'none';
                }
            });
        }

        // تحميل المستلمين بناءً على النوع
        function loadRecipients(type) {
            recipientsSelect.innerHTML = '<option value="" disabled>جاري تحميل المستلمين...</option>';

            // يمكن استخدام fetch للحصول على المستلمين من الخادم
            fetch(`/api/get_recipients?type=${type}`)
                .then(response => response.json())
                .then(data => {
                    recipientsSelect.innerHTML = '';
                    data.forEach(recipient => {
                        const option = document.createElement('option');
                        option.value = recipient.id;
                        option.textContent = recipient.name;
                        recipientsSelect.appendChild(option);
                    });
                })
                .catch(error => {
                    console.error('Error loading recipients:', error);
                    recipientsSelect.innerHTML = '<option value="" disabled>حدث خطأ أثناء تحميل المستلمين</option>';
                });
        }
    });

    // وظائف التعامل مع الإشعارات
    function viewNotification(id) {
        // عرض تفاصيل الإشعار
        console.log('عرض الإشعار رقم: ' + id);
        // يمكن استخدام fetch للحصول على البيانات من الخادم
    }

    function editNotification(id) {
        // تحميل بيانات الإشعار للتعديل
        console.log('تحميل بيانات الإشعار رقم: ' + id);
        // يمكن استخدام fetch للحصول على البيانات من الخادم
    }

    function resendNotification(id) {
        if (confirm('هل تريد إعادة إرسال هذا الإشعار؟')) {
            // إرسال طلب لإعادة إرسال الإشعار
            console.log('إعادة إرسال الإشعار رقم: ' + id);
        }
    }

    function deleteNotification(id) {
        if (confirm('هل أنت متأكد من حذف هذا الإشعار؟ لا يمكن التراجع عن هذا الإجراء.')) {
            // إرسال طلب لحذف الإشعار
            console.log('حذف الإشعار رقم: ' + id);
        }
    }

    // إرسال إشعار بناءً على سجل الغياب أو الاستئذان أو التأخر
    function sendNotification(recordId, recordType, studentId, studentName) {
        console.log('إرسال إشعار للطالب:', studentName, 'نوع السجل:', recordType);

        try {
            // فتح نافذة إضافة إشعار جديد
            const modalElement = document.getElementById('addNotificationModal');
            if (!modalElement) {
                console.error('لم يتم العثور على عنصر النافذة المنبثقة');
                alert('حدث خطأ أثناء فتح نافذة الإشعار');
                return;
            }

            // التحقق من وجود مكتبة Bootstrap
            if (typeof bootstrap === 'undefined' || !bootstrap.Modal) {
                console.error('مكتبة Bootstrap غير محملة بشكل صحيح');
                // استخدام طريقة بديلة لفتح النافذة المنبثقة
                $(modalElement).modal('show');
                return;
            }

            const modal = new bootstrap.Modal(modalElement);
            modal.show();
        } catch (error) {
            console.error('حدث خطأ أثناء فتح النافذة المنبثقة:', error);
            alert('حدث خطأ أثناء فتح نافذة الإشعار');
            return;
        }

        // تعيين نوع الإشعار إلى "إشعار لأولياء الأمور"
        const notificationType = document.getElementById('notification_type');
        notificationType.value = 'parents';

        // تحديد عنوان ومحتوى الإشعار بناءً على نوع السجل
        const titleInput = document.getElementById('notification_title');
        const contentInput = document.getElementById('notification_content');

        // تعيين التاريخ الحالي
        const dateInput = document.getElementById('notification_date');
        dateInput.value = new Date().toISOString().split('T')[0];

        // تفعيل إرسال الرسائل النصية وواتساب
        const sendSmsCheckbox = document.getElementById('send_sms');
        const sendWhatsappCheckbox = document.getElementById('send_whatsapp');
        sendSmsCheckbox.checked = true;
        sendWhatsappCheckbox.checked = true;



        // تعيين العنوان والمحتوى بناءً على نوع السجل
        if (recordType === 'absence') {
            titleInput.value = `إشعار غياب الطالب: ${studentName}`;
            contentInput.value = `نود إشعاركم بأن الطالب ${studentName} قد تغيب عن المدرسة اليوم. يرجى التواصل مع إدارة المدرسة لتوضيح سبب الغياب.`;
        } else if (recordType === 'permission') {
            titleInput.value = `إشعار استئذان الطالب: ${studentName}`;
            contentInput.value = `نود إشعاركم بأن الطالب ${studentName} قد استأذن من المدرسة اليوم. يرجى التأكد من وصوله إلى المنزل بسلام.`;
        } else if (recordType === 'tardiness') {
            titleInput.value = `إشعار تأخر الطالب: ${studentName}`;
            contentInput.value = `نود إشعاركم بأن الطالب ${studentName} قد تأخر عن الحضور إلى المدرسة اليوم. يرجى الحرص على وصوله في الوقت المحدد.`;
        }

        // تحميل ولي أمر الطالب المحدد فقط
        const recipientsContainer = document.getElementById('recipients_container');
        const recipientsSelect = document.getElementById('notification_recipients');

        // إظهار حقل المستلمين
        if (recipientsContainer) {
            recipientsContainer.style.display = 'block';
        }

        // تفريغ قائمة المستلمين وإضافة رسالة "جاري التحميل"
        if (recipientsSelect) {
            recipientsSelect.innerHTML = '<option value="" disabled>جاري تحميل بيانات ولي الأمر...</option>';

            // استخدام API الجديد للحصول على بيانات ولي أمر الطالب المحدد فقط
            fetch(`/api/get_student_parent?student_id=${studentId}`)
                .then(response => response.json())
                .then(data => {
                    recipientsSelect.innerHTML = '';

                    if (data && data.length > 0) {
                        const parent = data[0];
                        const option = document.createElement('option');
                        option.value = parent.id;
                        option.textContent = parent.name;
                        option.selected = true;
                        recipientsSelect.appendChild(option);
                        console.log('تم تحميل وتحديد ولي أمر الطالب:', parent.name);
                    } else {
                        console.warn('لم يتم العثور على بيانات ولي أمر الطالب');
                        recipientsSelect.innerHTML = '<option value="" disabled>لم يتم العثور على بيانات ولي الأمر</option>';
                    }
                })
                .catch(error => {
                    console.error('حدث خطأ أثناء تحميل بيانات ولي الأمر:', error);
                    recipientsSelect.innerHTML = '<option value="" disabled>حدث خطأ أثناء تحميل بيانات ولي الأمر</option>';
                });
        }
    }
</script>
{% endblock %}
