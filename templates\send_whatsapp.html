{% extends 'base.html' %}

{% block title %}إرسال رسائل واتساب - نظام إدارة المدرسة{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-3">
    <h2><i class="bi bi-whatsapp me-2" style="color: #25D366;"></i>إرسال رسائل واتساب</h2>
    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
        <a href="{{ url_for('notifications') }}" class="btn btn-secondary">
            <i class="bi bi-arrow-right me-1"></i>العودة إلى الإشعارات
        </a>
    </div>
</div>

<div class="mt-3 mb-4">
    <div class="d-flex gap-2 mb-2">
        <button type="button" class="btn btn-primary" id="autoSendBtn">
            <i class="bi bi-magic me-1"></i>إرسال تلقائي
        </button>
    </div>


</div>

<!-- معلومات الإشعار -->
<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0"><i class="bi bi-bell-fill me-2"></i>معلومات الإشعار</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <p><strong>العنوان:</strong> {{ notification.title }}</p>
                <p><strong>النوع:</strong>
                    {% if notification.type == 'all' %}
                        إشعار عام
                    {% elif notification.type == 'students' %}
                        إشعار للطلاب
                    {% elif notification.type == 'teachers' %}
                        إشعار للمعلمين
                    {% elif notification.type == 'parents' %}
                        إشعار لأولياء الأمور
                    {% endif %}
                </p>
            </div>
            <div class="col-md-6">
                <p><strong>التاريخ:</strong> {{ notification.date }}</p>
                <p><strong>عدد المستلمين:</strong> {{ notification.recipients_count }}</p>
            </div>
        </div>
        <div class="mt-2">
            <p><strong>محتوى الإشعار:</strong></p>
            <div class="p-3 bg-light rounded">{{ notification.content }}</div>
        </div>
    </div>
</div>

<!-- قائمة الرسائل -->
<div class="card">
    <div class="card-header bg-success text-white">
        <h5 class="mb-0"><i class="bi bi-whatsapp me-2"></i>رسائل واتساب للإرسال ({{ whatsapp_links|length }})</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th scope="col">#</th>
                        <th scope="col">المستلم</th>
                        <th scope="col">الطالب</th>
                        <th scope="col">رقم الهاتف</th>
                        <th scope="col">الحالة</th>
                        <th scope="col">إجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% if whatsapp_links %}
                        {% for link in whatsapp_links %}
                        <tr id="row_{{ loop.index }}">
                            <th scope="row">{{ loop.index }}</th>
                            <td>{{ link.recipient_name }}</td>
                            <td>{{ link.student_name }}</td>
                            <td dir="ltr">
                                <span class="badge bg-success">+{{ link.phone }}</span>
                            </td>
                            <td id="status_{{ loop.index }}">
                                <span class="badge bg-secondary">في الانتظار</span>
                            </td>
                            <td>
                                <button type="button" class="btn btn-success btn-sm" onclick="sendWhatsAppMessage('{{ link.url }}', {{ loop.index }})">
                                    <i class="bi bi-send me-1"></i>إرسال
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="6" class="text-center">لا توجد رسائل للإرسال</td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    // إرسال رسالة واتساب
    function sendWhatsAppMessage(url, index) {
        // تحديث حالة الرسالة
        const statusElement = document.getElementById(`status_${index}`);
        statusElement.innerHTML = '<span class="badge bg-warning text-dark">جاري الإرسال...</span>';

        // تعيين قيمة افتراضية لاستخدام رقم الهاتف المحدد في الإعدادات
        const useSettingsPhone = true;

        // استخدام واجهة برمجة التطبيقات Fetch لإرسال الرسالة
        fetch('/api/send_whatsapp', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                url: url,
                index: index,
                use_settings_phone: useSettingsPhone
            }),
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تحديث حالة الرسالة بعد الإرسال الناجح
                statusElement.innerHTML = '<span class="badge bg-success">تم الإرسال</span>';

                // تغيير لون الصف للإشارة إلى أنه تم التعامل معه
                const row = document.getElementById(`row_${index}`);
                row.classList.add('table-success');
            } else {
                // تحديث حالة الرسالة في حالة الفشل
                statusElement.innerHTML = '<span class="badge bg-danger">فشل الإرسال</span>';
                alert(`فشل إرسال الرسالة: ${data.error}`);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            statusElement.innerHTML = '<span class="badge bg-danger">خطأ في الإرسال</span>';
            alert('حدث خطأ أثناء محاولة إرسال الرسالة. يرجى المحاولة مرة أخرى.');
        });


    }

    // تم إزالة وظيفة إرسال الكل وتوحيدها مع وظيفة الإرسال التلقائي

    // إرسال تلقائي للرسائل
    document.getElementById('autoSendBtn').addEventListener('click', function() {
        // تغيير نص الزر وتعطيله أثناء الإرسال
        const autoSendBtn = document.getElementById('autoSendBtn');
        autoSendBtn.innerHTML = '<i class="bi bi-hourglass-split me-1"></i>جاري الإرسال التلقائي...';
        autoSendBtn.disabled = true;

        // الحصول على جميع الروابط
        const links = [
            {% for link in whatsapp_links %}
                { url: "{{ link.url }}", index: {{ loop.index }}, phone: "{{ link.phone }}", message: "{{ notification.title }}: {{ notification.content|replace('\n', ' ')|replace('"', '\\"') }}" }{% if not loop.last %},{% endif %}
            {% endfor %}
        ];

        // تعيين قيمة افتراضية لاستخدام رقم الهاتف المحدد في الإعدادات
        const useSettingsPhone = true;

        // إرسال الرسائل تلقائيًا باستخدام واجهة برمجة التطبيقات
        fetch('/api/auto_send_whatsapp', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                links: links,
                notification_id: "{{ notification.id }}",
                notification_title: "{{ notification.title }}",
                notification_content: "{{ notification.content|replace('\n', ' ')|replace('"', '\\"') }}",
                use_settings_phone: useSettingsPhone
            }),
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تحديث حالة جميع الرسائل
                links.forEach(link => {
                    const statusElement = document.getElementById(`status_${link.index}`);
                    statusElement.innerHTML = '<span class="badge bg-success">تم الإرسال تلقائيًا</span>';

                    const row = document.getElementById(`row_${link.index}`);
                    row.classList.add('table-success');
                });

                // تحديث نص الزر
                autoSendBtn.innerHTML = '<i class="bi bi-check-circle me-1"></i>تم الإرسال التلقائي بنجاح';
                autoSendBtn.classList.remove('btn-primary');
                autoSendBtn.classList.add('btn-success');

                // عرض رسالة نجاح
                alert('تم إرسال جميع الرسائل تلقائيًا بنجاح!');
            } else {
                // تحديث نص الزر في حالة الفشل
                autoSendBtn.innerHTML = '<i class="bi bi-exclamation-triangle me-1"></i>فشل الإرسال التلقائي';
                autoSendBtn.classList.remove('btn-primary');
                autoSendBtn.classList.add('btn-warning');
                autoSendBtn.disabled = false;

                // عرض رسالة خطأ
                alert(`فشل الإرسال التلقائي: ${data.error}`);
            }
        })
        .catch(error => {
            console.error('Error:', error);

            // تحديث نص الزر في حالة الخطأ
            autoSendBtn.innerHTML = '<i class="bi bi-x-circle me-1"></i>حدث خطأ أثناء الإرسال التلقائي';
            autoSendBtn.classList.remove('btn-primary');
            autoSendBtn.classList.add('btn-danger');
            autoSendBtn.disabled = false;

            // عرض رسالة خطأ
            alert('حدث خطأ أثناء محاولة الإرسال التلقائي. يرجى المحاولة مرة أخرى.');
        });
    });
</script>
{% endblock %}
