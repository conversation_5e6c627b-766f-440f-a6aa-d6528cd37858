{% extends 'base.html' %}

{% block title %}إضافة مخالفة سلوكية{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1 class="mb-4">إضافة مخالفة سلوكية جديدة</h1>

    <form method="POST" action="{{ url_for('add_violation') }}">
        <div class="row mb-3">
            <div class="col-md-6">
                <label for="student_search" class="form-label">بحث عن طالب (اكتب جزء من الاسم):</label>
                <input type="text" class="form-control" id="student_search" placeholder="أدخل اسم الطالب للبحث...">
                <div id="student_suggestions" class="list-group mt-2"></div>
                <input type="hidden" id="student_id" name="student_id">
                <p id="selected_student_name" class="mt-2"></p>
            </div>
            <div class="col-md-6">
                <label for="violation_date" class="form-label">تاريخ المخالفة:</label>
                <input type="date" class="form-control" id="violation_date" name="violation_date" required>
            </div>
        </div>

        <!-- درجة المخالفة والمخالفة السلوكية -->
        <div class="row mb-3">
            <div class="col-md-6">
                <label for="violation_degree" class="form-label">درجة المخالفة:</label>
                <select class="form-select" id="violation_degree" name="violation_degree" required>
                    <option value="الدرجة الاولى">الدرجة الاولى</option>
                    <option value="الدرجة الثانية">الدرجة الثانية</option>
                    <option value="الدرجة الثالثة">الدرجة الثالثة</option>
                    <option value="الدرجة الرابعة">الدرجة الرابعة</option>
                    <option value="الدرجة الخامسة">الدرجة الخامسة</option>
                    <option value="الدرجة الرابعة - بمنسوبي المدرسة">الدرجة الرابعة - بمنسوبي المدرسة</option>
                    <option value="الدرجة الخامسة - بمنسوبي المدرسة">الدرجة الخامسة - بمنسوبي المدرسة</option>
                </select>
            </div>
            <div class="col-md-6">
                <label for="behavioral_violation" class="form-label">المخالفة السلوكية:</label>
                <input type="text" class="form-control" id="behavioral_violation" name="behavioral_violation" placeholder="أدخل وصف المخالفة" required>
            </div>
        </div>

        <!-- Removed the separate row for violation_date as it's now combined with student_search -->

        <div class="mb-3">
            <label for="violation_description" class="form-label">وصف المخالفة:</label>
            <textarea class="form-control" id="violation_description" name="violation_description" rows="3" required></textarea>
        </div>

        <div class="mb-3">
            <label for="action_taken" class="form-label">الإجراء المتخذ:</label>
            <input type="text" class="form-control" id="action_taken" name="action_taken">
        </div>

        <button type="submit" class="btn btn-primary">إضافة المخالفة</button>
        <a href="{{ url_for('behavioral_violations') }}" class="btn btn-secondary">إلغاء</a>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('student_search');
    const suggestionsDiv = document.getElementById('student_suggestions');
    const studentIdInput = document.getElementById('student_id');
    const selectedStudentNameP = document.getElementById('selected_student_name');

    searchInput.addEventListener('input', function() {
        const query = searchInput.value.trim();
        suggestionsDiv.innerHTML = ''; // مسح الاقتراحات السابقة
        studentIdInput.value = ''; // مسح الـ ID المخفي
        selectedStudentNameP.textContent = ''; // مسح اسم الطالب المختار المعروض

        if (query.length < 2) { // لا تبحث إلا بعد حرفين على الأقل
            return;
        }

        // استدعاء API للبحث عن الطلاب
        fetch(`/api/search_students?query=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(students => {
                students.forEach(student => {
                    const suggestionItem = document.createElement('a');
                    suggestionItem.href = '#';
                    suggestionItem.classList.add('list-group-item', 'list-group-item-action');
                    suggestionItem.textContent = `${student.name} (الصف: ${student.class})`; // عرض الاسم والصف
                    suggestionItem.dataset.id = student.id;
                    suggestionItem.dataset.name = student.name;

                    suggestionItem.addEventListener('click', function(e) {
                        e.preventDefault();
                        searchInput.value = ''; // مسح حقل البحث
                        studentIdInput.value = student.id; // تعيين الـ ID المخفي
                        selectedStudentNameP.textContent = `الطالب المختار: ${student.name}`; // عرض اسم الطالب المختار
                        suggestionsDiv.innerHTML = ''; // إخفاء قائمة الاقتراحات
                    });
                    suggestionsDiv.appendChild(suggestionItem);
                });
            })
            .catch(error => console.error('Error fetching students:', error));
    });
});
</script>

{% endblock %}