{% extends 'base.html' %}

{% block title %}إضافة معلم جديد - نظام إدارة المدرسة{% endblock %}

{% block content %}
<h2><i class="bi bi-person-plus-fill me-2"></i>إضافة معلم جديد</h2>
<hr>
<form method="POST" action="{{ url_for('add_teacher') }}">
    <div class="mb-3">
        <label for="teacher_id" class="form-label">معرف المعلم</label>
        <input type="text" class="form-control" id="teacher_id" name="teacher_id" required>
        <div id="idHelp" class="form-text">أدخل معرفًا فريدًا للمعلم.</div>
    </div>
    <div class="mb-3">
        <label for="name" class="form-label">اسم المعلم</label>
        <input type="text" class="form-control" id="name" name="name" required>
    </div>
    <div class="mb-3">
        <label for="contact" class="form-label">معلومات الاتصال</label>
        <input type="text" class="form-control" id="contact" name="contact" placeholder="رقم الهاتف أو البريد الإلكتروني">
    </div>
    <div class="mb-3">
        <label for="specialization" class="form-label">التخصص</label>
        <input type="text" class="form-control" id="specialization" name="specialization" required>
    </div>
    <button type="submit" class="btn btn-primary"><i class="bi bi-check-circle-fill me-1"></i> إضافة المعلم</button>
    <a href="{{ url_for('list_teachers') }}" class="btn btn-secondary"><i class="bi bi-x-circle-fill me-1"></i> إلغاء</a>
</form>
{% endblock %}