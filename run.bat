@echo off
echo تشغيل نظام إدارة المدرسة...
echo.

REM تشغيل البرنامج باستخدام Python
python run_app.py

REM إذا فشل التشغيل، حاول استخدام python3
if %errorlevel% neq 0 (
    echo محاولة استخدام python3...
    python3 run_app.py
)

REM إذا فشلت جميع المحاولات، أظهر رسالة خطأ
if %errorlevel% neq 0 (
    echo.
    echo فشل تشغيل البرنامج. يرجى التأكد من تثبيت Python على جهازك.
    echo يمكنك تثبيت Python من الموقع الرسمي: https://www.python.org/downloads/
    echo.
    pause
)
