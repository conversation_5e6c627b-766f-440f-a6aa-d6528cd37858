{% extends 'base.html' %}

{% block title %}تعديل بيانات الطالب - نظام إدارة المدرسة{% endblock %}

{% block content %}
<h2><i class="bi bi-pencil-square me-2"></i>تعديل بيانات الطالب: {{ student.name }} (المعرف: {{ student_id }})</h2>
<hr>
<form method="POST" action="{{ url_for('edit_student', student_id=student_id) }}">
    <div class="mb-3">
        <label for="name" class="form-label">اسم الطالب</label>
        <input type="text" class="form-control" id="name" name="name" value="{{ student.name }}" required>
    </div>
    <div class="mb-3">
        <label for="grade" class="form-label">الصف</label>
        <select class="form-select" id="grade" name="grade" required>
            <option value="" disabled>اختر الصف...</option>
            <option value="الأول المتوسط" {% if student.grade == 'الأول المتوسط' %}selected{% endif %}>الأول المتوسط</option>
            <option value="الثاني المتوسط" {% if student.grade == 'الثاني المتوسط' %}selected{% endif %}>الثاني المتوسط</option>
            <option value="الثالث المتوسط" {% if student.grade == 'الثالث المتوسط' %}selected{% endif %}>الثالث المتوسط</option>
        </select>
    </div>
    <div class="mb-3">
        <label for="classroom" class="form-label">الفصل</label>
        <select class="form-select" id="classroom" name="classroom" required>
            <option value="" disabled>اختر الفصل...</option>
            <option value="1" {% if student.classroom == '1' %}selected{% endif %}>1</option>
            <option value="2" {% if student.classroom == '2' %}selected{% endif %}>2</option>
            <option value="3" {% if student.classroom == '3' %}selected{% endif %}>3</option>
            <option value="4" {% if student.classroom == '4' %}selected{% endif %}>4</option>
            <option value="5" {% if student.classroom == '5' %}selected{% endif %}>5</option>
        </select>
    </div>
    <div class="mb-3">
        <label for="contact" class="form-label">رقم الجوال</label>
        <div class="input-group">
            {# يتم تمرير contact_number من الواجهة الخلفية وهو الجزء المكون من 9 أرقام فقط #}
            <input type="tel" class="form-control" id="contact" name="contact_number" value="{{ contact_number }}" required pattern="^5\d{8}$" title="يجب إدخال 9 أرقام تبدأ بالرقم 5" placeholder="5xxxxxxxx" maxlength="9">
            <span class="input-group-text">+966</span>
        </div>
    </div>
    <button type="submit" class="btn btn-primary"><i class="bi bi-save-fill me-1"></i> حفظ التعديلات</button>
    <a href="{{ url_for('list_students') }}" class="btn btn-secondary"><i class="bi bi-x-circle-fill me-1"></i> إلغاء</a>
</form>
{% endblock %}