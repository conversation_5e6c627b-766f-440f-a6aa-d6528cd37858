"""
برنامج لتشغيل تطبيق نظام إدارة المدرسة وفتح المتصفح تلقائياً
"""

import os
import sys
import subprocess
import webbrowser
import time
import threading

def open_browser():
    """فتح المتصفح بعد تأخير قصير"""
    # انتظار 3 ثوانٍ لإعطاء وقت للخادم للبدء
    time.sleep(3)
    print("جاري فتح المتصفح...")
    webbrowser.open("http://127.0.0.1:5000")

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    print("=" * 50)
    print("تشغيل نظام إدارة المدرسة")
    print("=" * 50)
    print("")
    
    # تشغيل التطبيق
    print("جاري تشغيل التطبيق...")
    
    # بدء مؤقت لفتح المتصفح
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        # تشغيل التطبيق مباشرة
        subprocess.run([sys.executable, "app.py"], check=True)
    except subprocess.CalledProcessError:
        print("حدث خطأ أثناء تشغيل التطبيق.")
        input("اضغط أي مفتاح للخروج...")
    except KeyboardInterrupt:
        print("\nتم إيقاف التطبيق بواسطة المستخدم.")
    except Exception as e:
        print(f"حدث خطأ غير متوقع: {e}")
        input("اضغط أي مفتاح للخروج...")

if __name__ == "__main__":
    main()
