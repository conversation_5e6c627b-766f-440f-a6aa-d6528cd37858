import os
import sys
import subprocess
import time
import webbrowser

def check_python():
    """التحقق من وجود Python"""
    print("جاري التحقق من وجود Python...")
    try:
        subprocess.check_call([sys.executable, "--version"])
        print("Python موجود على جهازك.")
        return True
    except:
        print("خطأ: Python غير مثبت على جهازك.")
        print("يرجى تثبيت Python من الموقع الرسمي: https://www.python.org/downloads/")
        return False

def install_requirements():
    """تثبيت المكتبات المطلوبة"""
    print("جاري تثبيت المكتبات المطلوبة...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("تم تثبيت جميع المكتبات المطلوبة بنجاح.")
        return True
    except Exception as e:
        print(f"خطأ في تثبيت المكتبات: {e}")
        return False

def run_app():
    """تشغيل التطبيق"""
    print("جاري تشغيل التطبيق...")
    try:
        # تشغيل التطبيق في عملية منفصلة
        if os.name == 'nt':  # Windows
            process = subprocess.Popen([sys.executable, "app.py"], creationflags=subprocess.CREATE_NEW_CONSOLE)
        else:  # Linux/Mac
            process = subprocess.Popen([sys.executable, "app.py"])
        
        print("تم تشغيل التطبيق بنجاح.")
        return process
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")
        return None

def open_browser():
    """فتح المتصفح"""
    print("انتظر 5 ثوانٍ حتى يتم تشغيل الخادم...")
    time.sleep(5)
    
    print("جاري فتح المتصفح...")
    try:
        webbrowser.open("http://127.0.0.1:5000")
        print("تم فتح المتصفح بنجاح.")
        return True
    except Exception as e:
        print(f"خطأ في فتح المتصفح: {e}")
        print("يرجى فتح المتصفح يدوياً والانتقال إلى: http://127.0.0.1:5000")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("تشغيل نظام إدارة المدرسة")
    print("=" * 50)
    print()
    
    # التحقق من وجود Python
    if not check_python():
        input("اضغط أي مفتاح للخروج...")
        return
    
    # تثبيت المكتبات المطلوبة
    if not install_requirements():
        input("اضغط أي مفتاح للخروج...")
        return
    
    # تشغيل التطبيق
    process = run_app()
    if not process:
        input("اضغط أي مفتاح للخروج...")
        return
    
    # فتح المتصفح
    open_browser()
    
    print()
    print("=" * 50)
    print("ملاحظات هامة:")
    print("1. إذا لم يفتح المتصفح تلقائياً، يرجى فتح المتصفح يدوياً والانتقال إلى:")
    print("   http://127.0.0.1:5000")
    print("2. لإيقاف البرنامج، أغلق نافذة موجه الأوامر التي تعرض رسائل الخادم.")
    print("=" * 50)
    
    input("اضغط أي مفتاح للخروج من هذه النافذة...")

if __name__ == "__main__":
    main()
