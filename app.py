#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
نظام إدارة المدرسة
برنامج لإدارة شؤون المدرسة من حضور وغياب وإشعارات
"""

# استيراد المكتبات القياسية
import json
import os
import threading
import time
import urllib.parse
import uuid
from datetime import datetime, timedelta

# استيراد مكتبات الطرف الثالث
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session
from werkzeug.utils import secure_filename
import pywhatkit
import pyautogui

# إضافة مكتبات Selenium للتحكم في المتصفح
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

print("Starting app.py execution...")
print("This application will run on http://127.0.0.1:5000")
# from flask_wtf import FlaskForm # Import FlaskForm - تم تعطيله مؤقتًا

app = Flask(__name__)
# Ensure imports for render_template if not already present at the top
# from flask import render_template # This line is likely already there or similar
app.secret_key = 'your_secret_key'  # استبدل بمفتاح سري حقيقي في بيئة الإنتاج

# إضافة دالة مساعدة لتحميل الإعدادات في القوالب
@app.context_processor
def utility_processor():
    def get_settings():
        return load_data(SETTINGS_FILE)

    def now():
        return datetime.now()

    return {
        'settings': get_settings(),
        'now': now
    }

# تحديد مجلد البيانات
DATA_DIR = 'data'

# التأكد من وجود مجلد البيانات
if not os.path.exists(DATA_DIR):
    os.makedirs(DATA_DIR)

STUDENTS_FILE = os.path.join(DATA_DIR, 'students.json')
TEACHERS_FILE = os.path.join(DATA_DIR, 'teachers.json')
ATTENDANCE_FILE = os.path.join(DATA_DIR, 'attendance.json')
TEACHERS_ATTENDANCE_FILE = os.path.join(DATA_DIR, 'teachers_attendance.json')
VIOLATIONS_FILE = os.path.join(DATA_DIR, 'violations.json') # إضافة مسار ملف المخالفات
SETTINGS_FILE = os.path.join(DATA_DIR, 'settings.json') # إضافة مسار ملف الإعدادات
NOTIFICATIONS_FILE = os.path.join(DATA_DIR, 'notifications.json') # إضافة مسار ملف الإشعارات

# تكوين مجلد تحميل الملفات
UPLOAD_FOLDER = os.path.join('static', 'uploads')
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}

# تكوين Flask لاستخدام مجلد التحميل
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER

# دالة مساعدة لتحضير رسالة واتساب المحسنة
def _prepare_whatsapp_message(message, record_type=None, record_date=None):
    """
    تحضير نص الرسالة المحسّن لإرسالها عبر واتساب.

    Args:
        message (str): نص الرسالة الأصلي.
        record_type (str, optional): نوع السجل (غياب، تأخر، استئذان).
        record_date (str, optional): تاريخ السجل.

    Returns:
        str: نص الرسالة المحسّن.
    """
    enhanced_message = message

    # التحقق من نوع السجل من عنوان الرسالة إذا لم يتم توفيره
    if not record_type:
        if "غياب" in message:
            record_type = "غياب"
        elif "تأخر" in message:
            record_type = "تأخر"
        elif "استئذان" in message:
            record_type = "استئذان"

    # إضافة معلومات عن نوع السجل وتاريخه إذا لم تكن موجودة بالفعل في الرسالة
    if record_type and "نوع الإشعار" not in message:
        enhanced_message += f"\n\nنوع الإشعار: {record_type}"

        # إضافة تاريخ السجل إذا كان متوفرًا
        if record_date:
            enhanced_message += f"\nتاريخ التسجيل: {record_date}"
        else:
            # استخدام تاريخ اليوم إذا لم يتم توفير تاريخ
            today_date = datetime.now().strftime('%Y-%m-%d')
            enhanced_message += f"\nتاريخ التسجيل: {today_date}"
    return enhanced_message

# وظيفة مساعدة لإرسال رسائل واتساب بشكل تلقائي بالكامل باستخدام Selenium
def send_whatsapp_selenium(phone, message, record_type=None, record_date=None):
    """
    إرسال رسالة واتساب بشكل تلقائي بالكامل باستخدام Selenium

    Args:
        phone (str): رقم الهاتف مع رمز الدولة (مثل +966123456789)
        message (str): نص الرسالة
        record_type (str, optional): نوع السجل (غياب، تأخر، استئذان)
        record_date (str, optional): تاريخ السجل

    Returns:
        bool: True إذا تم الإرسال بنجاح، False إذا فشل الإرسال
    """
    driver = None
    try:
        enhanced_message = _prepare_whatsapp_message(message, record_type, record_date)

        # تنظيف رقم الهاتف
        if phone.startswith('+'):
            phone = phone[1:]  # إزالة علامة '+'

        # إعداد خيارات المتصفح
        chrome_options = Options()
        chrome_options.add_argument("--start-maximized")  # تكبير النافذة
        chrome_options.add_argument("--disable-notifications")  # تعطيل الإشعارات
        chrome_options.add_argument("--disable-infobars")  # تعطيل شريط المعلومات
        chrome_options.add_argument("--disable-extensions")  # تعطيل الإضافات

        # إنشاء متصفح جديد
        driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)

        try:
            # فتح واتساب ويب مباشرة مع رقم الهاتف والرسالة
            whatsapp_url = f"https://web.whatsapp.com/send?phone={phone}&text={urllib.parse.quote(enhanced_message)}"
            driver.get(whatsapp_url)

            # انتظار ظهور زر الإرسال (يشير إلى أن واتساب ويب جاهز)
            # زيادة وقت الانتظار إلى 120 ثانية للتأكد من تحميل واتساب ويب بشكل كامل
            send_button = WebDriverWait(driver, 120).until(
                EC.element_to_be_clickable((By.XPATH, "//span[@data-icon='send']"))
            )

            # انتظار لحظة قبل الضغط على زر الإرسال
            time.sleep(3)

            # محاولة الضغط على زر الإرسال باستخدام JavaScript
            driver.execute_script("arguments[0].click();", send_button)

            # محاولة ثانية باستخدام الطريقة العادية إذا لم تنجح الطريقة الأولى
            try:
                send_button.click()
            except:
                pass

            # انتظار لحظة للتأكد من إرسال الرسالة
            time.sleep(5)

            # محاولة التحقق من نجاح الإرسال عن طريق البحث عن علامة "تم التسليم" أو "تم الإرسال"
            try:
                # انتظار ظهور علامة الإرسال
                WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, "//span[contains(@aria-label, 'تم') or contains(@aria-label, 'Delivered') or contains(@aria-label, 'Sent')]"))
                )
            except:
                # حتى لو لم نجد علامة الإرسال، نفترض أن الرسالة تم إرسالها
                pass

            # إغلاق المتصفح
            driver.quit()
            driver = None

            return True
        except Exception as e:
            print(f"خطأ أثناء إرسال الرسالة: {e}")
            # محاولة إضافية باستخدام pyautogui للضغط على Enter
            try:
                # انتظار لحظة إضافية
                time.sleep(2)
                # الضغط على Enter
                pyautogui.press('enter')
                # انتظار للتأكد من الإرسال
                time.sleep(3)
                # إغلاق المتصفح
                if driver:
                    driver.quit()
                    driver = None
                return True
            except:
                # إغلاق المتصفح في حالة حدوث خطأ
                if driver:
                    driver.quit()
                    driver = None
                return False
    except Exception as e:
        print(f"خطأ في إرسال رسالة واتساب: {e}")
        # إغلاق المتصفح في حالة حدوث خطأ
        if driver:
            driver.quit()
        return False

# وظيفة مساعدة لإرسال رسائل واتساب بشكل تلقائي بالكامل باستخدام طريقة مباشرة
def send_whatsapp_direct(phone, message, record_type=None, record_date=None):
    """
    إرسال رسالة واتساب بشكل تلقائي بالكامل باستخدام طريقة مباشرة

    Args:
        phone (str): رقم الهاتف مع رمز الدولة (مثل +966123456789)
        message (str): نص الرسالة
        record_type (str, optional): نوع السجل (غياب، تأخر، استئذان)
        record_date (str, optional): تاريخ السجل

    Returns:
        bool: True إذا تم الإرسال بنجاح، False إذا فشل الإرسال
    """
    driver = None
    try:
        enhanced_message = _prepare_whatsapp_message(message, record_type, record_date)

        # تنظيف رقم الهاتف
        if phone.startswith('+'):
            phone = phone[1:]  # إزالة علامة '+'

        # إعداد خيارات المتصفح
        chrome_options = Options()
        chrome_options.add_argument("--start-maximized")  # تكبير النافذة
        chrome_options.add_argument("--disable-notifications")  # تعطيل الإشعارات
        chrome_options.add_argument("--disable-infobars")  # تعطيل شريط المعلومات
        chrome_options.add_argument("--disable-extensions")  # تعطيل الإضافات

        # إنشاء متصفح جديد
        driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)

        try:
            # فتح واتساب ويب أولاً
            driver.get("https://web.whatsapp.com/")

            # انتظار تحميل واتساب ويب (انتظار ظهور عنصر البحث)
            WebDriverWait(driver, 120).until(
                EC.presence_of_element_located((By.XPATH, "//div[@contenteditable='true' and @data-tab='3']"))
            )

            # انتظار لحظة للتأكد من تحميل واتساب ويب بشكل كامل
            time.sleep(5)

            # الآن نقوم بفتح محادثة جديدة مع الرقم المطلوب
            driver.get(f"https://web.whatsapp.com/send?phone={phone}")

            # انتظار تحميل المحادثة (انتظار ظهور حقل إدخال الرسالة)
            input_box = WebDriverWait(driver, 60).until(
                EC.presence_of_element_located((By.XPATH, "//div[@contenteditable='true' and @data-tab='10']"))
            )

            # انتظار لحظة للتأكد من تحميل المحادثة بشكل كامل
            time.sleep(3)

            # إدخال الرسالة في حقل الإدخال
            input_box.clear()

            # إدخال الرسالة حرفاً حرفاً للتأكد من إدخالها بشكل صحيح
            for character in enhanced_message:
                input_box.send_keys(character)
                time.sleep(0.01)  # انتظار قصير بين كل حرف

            # انتظار لحظة قبل الضغط على زر الإرسال
            time.sleep(2)

            # الضغط على Enter لإرسال الرسالة
            input_box.send_keys(Keys.ENTER)

            # انتظار لحظة للتأكد من إرسال الرسالة
            time.sleep(5)

            # إغلاق المتصفح
            driver.quit()
            driver = None

            return True
        except Exception as e:
            print(f"خطأ أثناء إرسال الرسالة بالطريقة المباشرة: {e}")
            # محاولة إضافية باستخدام pyautogui للضغط على Enter
            try:
                # انتظار لحظة إضافية
                time.sleep(2)
                # الضغط على Enter
                pyautogui.press('enter')
                # انتظار للتأكد من الإرسال
                time.sleep(3)
                # إغلاق المتصفح
                if driver:
                    driver.quit()
                    driver = None
                return True
            except:
                # إغلاق المتصفح في حالة حدوث خطأ
                if driver:
                    driver.quit()
                    driver = None
                return False
    except Exception as e:
        print(f"خطأ في إرسال رسالة واتساب بالطريقة المباشرة: {e}")
        # إغلاق المتصفح في حالة حدوث خطأ
        if driver:
            driver.quit()
        return False

# وظيفة مساعدة لإرسال رسائل واتساب بشكل تلقائي بالكامل باستخدام pywhatkit (احتياطية)
def send_whatsapp_auto(phone, message, record_type=None, record_date=None):
    """
    إرسال رسالة واتساب بشكل تلقائي بالكامل باستخدام pywhatkit وpyautogui

    Args:
        phone (str): رقم الهاتف مع رمز الدولة (مثل +966123456789)
        message (str): نص الرسالة
        record_type (str, optional): نوع السجل (غياب، تأخر، استئذان)
        record_date (str, optional): تاريخ السجل

    Returns:
        bool: True إذا تم الإرسال بنجاح، False إذا فشل الإرسال
    """
    try:
        enhanced_message = _prepare_whatsapp_message(message, record_type, record_date)

        # الحصول على الوقت الحالي
        now = datetime.now()
        current_hour = now.hour
        current_minute = now.minute + 1  # إضافة دقيقة واحدة للسماح بتحميل واتساب

        # تعديل الساعة إذا تجاوزت الدقائق 59
        if current_minute >= 60:
            current_hour += 1
            current_minute %= 60

        # استخدام pywhatkit لفتح واتساب ويب وكتابة الرسالة المحسنة
        pywhatkit.sendwhatmsg(phone, enhanced_message, current_hour, current_minute, 15, True, 5)

        # انتظار لحظة للتأكد من تحميل واتساب ويب
        time.sleep(3)

        # استخدام pyautogui للضغط على زر الإرسال بطرق متعددة
        def press_enter_multiple():
            # انتظار لحظة قبل الضغط
            time.sleep(2)

            # محاولة أولى: الضغط على Enter
            pyautogui.press('enter')
            time.sleep(1)

            # محاولة ثانية: الضغط على Enter مرة أخرى
            pyautogui.press('enter')
            time.sleep(1)

            # محاولة ثالثة: محاكاة النقر بالماوس في وسط الشاشة ثم الضغط على Enter
            screen_width, screen_height = pyautogui.size()
            pyautogui.click(screen_width // 2, screen_height // 2)
            time.sleep(0.5)
            pyautogui.press('enter')

            # محاولة رابعة: محاكاة النقر في الجزء السفلي الأيمن من الشاشة (حيث يكون زر الإرسال عادة)
            pyautogui.click(screen_width - 100, screen_height - 100)
            time.sleep(0.5)
            pyautogui.press('enter')

        # تشغيل وظيفة الضغط على زر الإرسال في خيط منفصل
        enter_thread = threading.Thread(target=press_enter_multiple)
        enter_thread.start()

        # انتظار انتهاء الخيط (بحد أقصى 10 ثوانٍ)
        enter_thread.join(timeout=10)

        # انتظار إضافي للتأكد من إكمال العملية
        time.sleep(2)

        return True
    except Exception as e:
        print(f"خطأ في إرسال رسالة واتساب: {e}")
        # محاولة أخيرة للضغط على Enter
        try:
            pyautogui.press('enter')
        except:
            pass
        return False

def allowed_file(filename):
    """التحقق من أن امتداد الملف مسموح به"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def load_data(filename):
    """تحميل البيانات من ملف JSON. تُرجع قاموسًا أو قائمة فارغة حسب المتوقع إذا كان الملف غير موجود أو فارغًا أو غير صالح."""
    # تحديد النوع الافتراضي المتوقع بناءً على اسم الملف
    default_empty = [] if filename.endswith('teachers.json') or filename.endswith('violations.json') else {}

    if not os.path.exists(filename):
        return default_empty
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
            if not content:
                return default_empty # إرجاع النوع الفارغ المناسب إذا كان الملف فارغًا
            data = json.loads(content)
            # إرجاع البيانات المحملة إذا كانت من النوع المتوقع (أو أي نوع صالح)
            # يمكن إضافة تحقق أكثر تفصيلاً هنا إذا لزم الأمر
            return data
    except json.JSONDecodeError:
        print(f"Error decoding JSON from {filename}. Returning default empty value.")
        return default_empty
    except FileNotFoundError:
        # هذا الشرط تمت تغطيته بـ os.path.exists، لكنه موجود للأمان
        return default_empty

def save_data(filename, data):
    # التأكد من وجود المجلد الأصل قبل الحفظ
    dir_name = os.path.dirname(filename)
    if dir_name and not os.path.exists(dir_name):
        os.makedirs(dir_name)
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=4)

# --- Routes --- #

@app.route('/')
def index():
    return render_template('index.html')

# --- Teacher Attendance Route --- #

@app.route('/teacher_attendance', methods=['GET', 'POST'])
def teacher_attendance():
    today_date = datetime.now().strftime('%Y-%m-%d')
    teachers_attendance_data = load_data(TEACHERS_ATTENDANCE_FILE)

    if request.method == 'POST':
        # معالجة POST لحفظ الحضور
        date = request.form.get('attendance_date', today_date)
        if date not in teachers_attendance_data:
            teachers_attendance_data[date] = {}

        teachers_list = load_data(TEACHERS_FILE)
        for teacher in teachers_list:
            teacher_id = teacher['id']
            status = request.form.get(f'status_{teacher_id}')
            if status:
                teachers_attendance_data[date][teacher_id] = status
            elif teacher_id in teachers_attendance_data.get(date, {}):
                # إذا لم يتم تحديد حالة، قم بإزالتها
                del teachers_attendance_data[date][teacher_id]

        save_data(TEACHERS_ATTENDANCE_FILE, teachers_attendance_data)
        flash('تم حفظ سجل حضور المعلمين بنجاح!', 'success')
        return redirect(url_for('teacher_attendance'))

    # معالجة GET لعرض النموذج
    teachers_list = load_data(TEACHERS_FILE)

    # الحصول على سجلات الحضور للتاريخ المحدد (أو اليوم)
    display_date = request.args.get('attendance_date', today_date)
    today_attendance = teachers_attendance_data.get(display_date, {})

    return render_template('teacher_attendance.html',
                          teachers=teachers_list,
                          today_date=display_date,
                          today_attendance=today_attendance)

# --- Student Routes --- #

@app.route('/students')
def list_students():
    students_dict = load_data(STUDENTS_FILE)
    students_list = list(students_dict.values()) # تحويل القاموس إلى قائمة للمعالجة

    # الحصول على معايير البحث والتصفية من طلب GET
    search_query = request.args.get('search_query', '').lower()
    filter_grade = request.args.get('filter_grade', '')
    filter_classroom = request.args.get('filter_classroom', '')

    # تصفية الطلاب بناءً على معايير البحث والتصفية
    students_to_display_list = []
    for student_id, student_data in students_dict.items():
        matches_search = (search_query in student_data.get('name', '').lower() or
                          search_query in student_id.lower())
        matches_grade = (not filter_grade or student_data.get('grade') == filter_grade)
        matches_classroom = (not filter_classroom or student_data.get('classroom') == filter_classroom)

        if matches_search and matches_grade and matches_classroom:
            # إضافة student_id مرة أخرى للرجوع إلى القاموس إذا لزم الأمر لاحقًا
            # أو ببساطة تمرير القاموس الكامل
            # students_to_display_list.append(student_data)
            # الأفضل تمرير القاموس الأصلي للحفاظ على ID
            pass # سيتم إنشاء القاموس النهائي أدناه

    # إعادء القاموس المطلوب للقالب
    students_to_display = {
        student_id: student_data
        for student_id, student_data in students_dict.items()
        if (search_query in student_data.get('name', '').lower() or search_query in student_id.lower())
        and (not filter_grade or student_data.get('grade') == filter_grade)
        and (not filter_classroom or student_data.get('classroom') == filter_classroom)
    }


    # الحصول على القيم الفريدة للصفوف والفصول الدراسية للقوائم المنسدلة
    all_students_list = list(students_dict.values())
    unique_grades = sorted(list(set(s.get('grade') for s in all_students_list if s.get('grade'))))
    unique_classrooms = sorted(list(set(s.get('classroom') for s in all_students_list if s.get('classroom'))))

    return render_template('students.html',
                           students_to_display=students_to_display,
                           search_query=search_query,
                           filter_grade=filter_grade,
                           filter_classroom=filter_classroom,
                           unique_grades=unique_grades,
                           unique_classrooms=unique_classrooms)

@app.route('/add_student', methods=['GET', 'POST'])
def add_student():
    if request.method == 'POST':
        students_dict = load_data(STUDENTS_FILE)
        errors = []
        added_count = 0

        # التحقق مما إذا كان الطلب يحتوي على ملف Excel
        if 'excel_file' in request.files and request.files['excel_file'].filename != '':
            file = request.files['excel_file']
            if file and (file.filename.endswith('.xlsx') or file.filename.endswith('.xls')):
                try:
                    # تم تعطيل استخدام pandas لتجنب مشاكل التثبيت
                    # بدلاً من ذلك، نعرض رسالة للمستخدم
                    flash('تم تعطيل استيراد الطلاب من ملفات Excel مؤقتًا. يرجى إضافة الطلاب يدويًا.', 'warning')
                    return render_template('add_student.html')

                except Exception as e:
                    flash(f'حدث خطأ أثناء قراءة ملف Excel: {e}', 'danger')
                    return render_template('add_student.html')
            else:
                flash('الرجاء تحميل ملف Excel صالح (.xlsx أو .xls).', 'warning')
                return render_template('add_student.html')

        # معالجة الإضافة اليدوية (الكود الأصلي)
        else:
            student_id = request.form.get('student_id')
            if not student_id:
                 while True:
                     student_id = str(uuid.uuid4())[:8]
                     if student_id not in students_dict:
                         break
            elif student_id in students_dict:
                flash(f'المعرف {student_id} موجود بالفعل!', 'error')
                return render_template('add_student.html')

            new_student_data = {
                'name': request.form['name'],
                'grade': request.form['grade'],
                'classroom': request.form['classroom'],
                'contact': '+966' + request.form['contact_number']
            }
            students_dict[student_id] = new_student_data
            save_data(STUDENTS_FILE, students_dict)
            flash('تمت إضافة الطالب بنجاح!', 'success')
            return redirect(url_for('list_students'))

    # في حالة GET، فقط اعرض النموذج
    return render_template('add_student.html')

@app.route('/edit_student/<student_id>', methods=['GET', 'POST'])
def edit_student(student_id):
    students_dict = load_data(STUDENTS_FILE)
    if student_id not in students_dict:
        flash('الطالب غير موجود!', 'error')
        return redirect(url_for('list_students'))

    student_data = students_dict[student_id]

    if request.method == 'POST':
        # تحديث بيانات الطالب في القاموس
        student_data['name'] = request.form['name']
        student_data['grade'] = request.form['grade'] # التأكد من استخدام الأسماء الصحيحة
        student_data['classroom'] = request.form['classroom']
        student_data['contact'] = '+966' + request.form['contact_number'] # استخدام contact_number وإضافة البادئة
        # student_data['dob'] = request.form.get('dob') # تأكد من وجود الحقل إذا لزم الأمر

        save_data(STUDENTS_FILE, students_dict)
        flash('تم تحديث بيانات الطالب بنجاح!', 'success')
        return redirect(url_for('list_students'))

    # في حالة GET، اعرض النموذج مع بيانات الطالب الحالية
    # تمرير student_id أيضًا إذا كان القالب يحتاجه
    return render_template('edit_student.html', student=student_data, student_id=student_id)

@app.route('/delete_student/<student_id>', methods=['POST'])
def delete_student(student_id):
    students_dict = load_data(STUDENTS_FILE)
    if student_id in students_dict:
        del students_dict[student_id]
        save_data(STUDENTS_FILE, students_dict)
        flash('تم حذف الطالب بنجاح!', 'success')
    else:
        flash('الطالب غير موجود!', 'error')
    return redirect(url_for('list_students'))

# --- Teacher Routes --- #

@app.route('/teachers')
def list_teachers():
    teachers_list = load_data(TEACHERS_FILE)
    # تحويل قائمة المعلمين إلى قاموس باستخدام معرف المعلم كمفتاح
    teachers_dict = {teacher['id']: teacher for teacher in teachers_list}
    return render_template('teachers.html', teachers=teachers_dict)

@app.route('/add_teacher', methods=['GET', 'POST'])
def add_teacher():
    if request.method == 'POST':
        teachers = load_data(TEACHERS_FILE)
        new_teacher = {
            'id': str(len(teachers) + 1), # طريقة بسيطة لتوليد ID
            'name': request.form['name'],
            'specialization': request.form['specialization'],
            'contact': request.form['contact']
        }
        teachers.append(new_teacher)
        save_data(TEACHERS_FILE, teachers)
        flash('تمت إضافة المعلم بنجاح!', 'success')
        return redirect(url_for('list_teachers'))
    return render_template('add_teacher.html')

@app.route('/edit_teacher/<teacher_id>', methods=['GET', 'POST'])
def edit_teacher(teacher_id):
    teachers = load_data(TEACHERS_FILE)
    teacher = next((t for t in teachers if t['id'] == teacher_id), None)
    if not teacher:
        flash('المعلم غير موجود!', 'error')
        return redirect(url_for('list_teachers'))

    if request.method == 'POST':
        teacher['name'] = request.form['name']
        teacher['specialization'] = request.form['specialization']
        teacher['contact'] = request.form['contact']
        save_data(TEACHERS_FILE, teachers)
        flash('تم تحديث بيانات المعلم بنجاح!', 'success')
        return redirect(url_for('list_teachers'))

    return render_template('edit_teacher.html', teacher=teacher)

@app.route('/delete_teacher/<teacher_id>', methods=['POST'])
def delete_teacher(teacher_id):
    teachers_list = load_data(TEACHERS_FILE)
    teachers_list = [t for t in teachers_list if t['id'] != teacher_id]
    save_data(TEACHERS_FILE, teachers_list)
    flash('تم حذف المعلم بنجاح!', 'success')
    return redirect(url_for('list_teachers'))

# --- Attendance Routes --- #

@app.route('/record_student_attendance', methods=['GET', 'POST'])
def record_student_attendance():
    today_date = datetime.now().strftime('%Y-%m-%d')
    attendance_data = load_data(ATTENDANCE_FILE)

    if request.method == 'POST':
        # --- قسم معالجة POST لحفظ الحضور (يبقى كما هو) ---
        date = request.form.get('attendance_date', today_date)
        if date not in attendance_data:
            attendance_data[date] = {}

        students_dict = load_data(STUDENTS_FILE)
        for student_id in students_dict:
            status = request.form.get(f'status_{student_id}')
            if status:
                attendance_data[date][student_id] = status
            elif student_id in attendance_data.get(date, {}):
                 # إذا لم يتم تحديد حالة (ربما تم إلغاء تحديدها)، قم بإزالتها
                 del attendance_data[date][student_id]

        save_data(ATTENDANCE_FILE, attendance_data)
        flash('تم حفظ سجل الحضور بنجاح!', 'success')
        # إعادة التوجيه مع الحفاظ على معلمات البحث/التصفية الحالية وإضافة علامة النجاح
        redirect_args = request.args.to_dict()
        redirect_args['save_success'] = 'true'
        return redirect(url_for('record_student_attendance', **redirect_args))

    # --- قسم معالجة GET لعرض النموذج والطلاب المصفاة ---
    students_dict = load_data(STUDENTS_FILE)

    # الحصول على معايير البحث والتصفية من طلب GET
    search_query = request.args.get('search_query', '').lower()
    filter_grade = request.args.get('filter_grade', '')
    filter_classroom = request.args.get('filter_classroom', '')

    # تصفية الطلاب بناءً على معايير البحث والتصفية
    students_to_display = {
        student_id: student_data
        for student_id, student_data in students_dict.items()
        if (search_query in student_data.get('name', '').lower() or search_query in student_id.lower())
        and (not filter_grade or student_data.get('grade') == filter_grade)
        and (not filter_classroom or student_data.get('classroom') == filter_classroom)
    }

    # الحصول على القيم الفريدة للصفوف والفصول الدراسية للقوائم المنسدلة (من جميع الطلاب)
    all_students_list = list(students_dict.values())
    unique_grades = sorted(list(set(s.get('grade') for s in all_students_list if s.get('grade'))))
    unique_classrooms = sorted(list(set(s.get('classroom') for s in all_students_list if s.get('classroom'))))

    # الحصول على سجلات الحضور للتاريخ المحدد (أو اليوم)
    display_date = request.args.get('attendance_date', today_date)
    today_attendance = attendance_data.get(display_date, {})

    return render_template('record_student_attendance.html',
                           students_to_display=students_to_display, # تمرير الطلاب المصفاة
                           today_date=display_date, # استخدام التاريخ المحدد أو اليوم
                           today_attendance=today_attendance,
                           unique_grades=unique_grades, # تمرير القوائم المنسدلة
                           unique_classrooms=unique_classrooms,
                           search_query=search_query, # تمرير قيم البحث/التصفية للحفاظ عليها في النموذج
                           filter_grade=filter_grade,
                           filter_classroom=filter_classroom,
                           attendance_data=attendance_data) # Pass attendance data to template

# تم دمج منطق حفظ الحضور في دالة record_student_attendance أعلاه
# يمكن إزالة هذه الدالة المنفصلة الآن
# @app.route('/save_attendance', methods=['POST'])
# def save_attendance():
#     ...

# --- Reports Route --- #

@app.route('/student_reports')
def student_reports():
    students_dict = load_data(STUDENTS_FILE)

    # الحصول على معايير البحث والتصفية من طلب GET
    search_query = request.args.get('search_query', '').lower()
    filter_grade = request.args.get('filter_grade', '')
    filter_classroom = request.args.get('filter_classroom', '')

    # تصفية الطلاب بناءً على معايير البحث والتصفية
    students_to_display = {
        student_id: student_data
        for student_id, student_data in students_dict.items()
        if (search_query in student_data.get('name', '').lower() or search_query in student_id.lower())
        and (not filter_grade or student_data.get('grade') == filter_grade)
        and (not filter_classroom or student_data.get('classroom') == filter_classroom)
    }

    # الحصول على القيم الفريدة للصفوف والفصول الدراسية للقوائم المنسدلة (من جميع الطلاب)
    all_students_list = list(students_dict.values())
    unique_grades = sorted(list(set(s.get('grade') for s in all_students_list if s.get('grade'))))
    unique_classrooms = sorted(list(set(s.get('classroom') for s in all_students_list if s.get('classroom'))))

    # استرداد تفاصيل التقرير إذا تم إنشاؤها (من generate_report)
    report_details = session.pop('report_details', None) # استخدام session لنقل البيانات

    return render_template('student_reports.html',
                           students_to_display=students_to_display, # تمرير الطلاب المصفاة
                           unique_grades=unique_grades,
                           unique_classrooms=unique_classrooms,
                           search_query=search_query, # تمرير قيم البحث/التصفية للحفاظ عليها
                           filter_grade=filter_grade,
                           filter_classroom=filter_classroom,
                           report_details=report_details) # تمرير تفاصيل التقرير إن وجدت

@app.route('/teacher_reports')
def teacher_reports():
    teachers_list = load_data(TEACHERS_FILE)

    # استرداد تفاصيل التقرير إذا تم إنشاؤها
    report_details = session.pop('teacher_report_details', None)

    return render_template('teacher_reports.html',
                          teachers=teachers_list,
                          report_details=report_details)

@app.route('/generate_report', methods=['POST'])
def generate_report():
    student_id = request.form.get('student_id')
    start_date_str = request.form.get('start_date')
    end_date_str = request.form.get('end_date')

    if not student_id or not start_date_str or not end_date_str:
        flash('يرجى تحديد الطالب وتواريخ البدء والانتهاء.', 'error')
        # إعادة التوجيه مع الحفاظ على معلمات البحث/التصفية
        return redirect(url_for('student_reports', **request.args))

    try:
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
    except ValueError:
        flash('تنسيق التاريخ غير صالح. يرجى استخدام YYYY-MM-DD.', 'error')
        return redirect(url_for('student_reports', **request.args))

    students_dict = load_data(STUDENTS_FILE)
    student = students_dict.get(student_id) # الوصول المباشر باستخدام ID
    if not student:
        flash('الطالب المحدد غير موجود.', 'error')
        return redirect(url_for('student_reports', **request.args))

    attendance_data = load_data(ATTENDANCE_FILE)
    report_data = []
    total_present = 0
    total_absent = 0
    total_late = 0
    total_excused = 0
    total_days = 0

    current_date = start_date
    while current_date <= end_date:
        date_str = current_date.strftime('%Y-%m-%d')
        day_attendance = attendance_data.get(date_str, {})
        status = day_attendance.get(student_id, 'لم يسجل') # افتراضي إلى 'لم يسجل'

        report_data.append({
            'date': date_str,
            'status': status
        })

        if status == 'present':
            total_present += 1
        elif status == 'absent':
            total_absent += 1
        elif status == 'late':
            total_late += 1
        elif status == 'excused':
            total_excused += 1

        # نعد جميع الأيام التي تم تسجيلها كأيام دراسية فعلية
        if status in ['present', 'absent', 'late', 'excused']:
             total_days += 1

        current_date += timedelta(days=1)

    # حساب النسبة المئوية للحضور (تجنب القسمة على صفر)
    attendance_percentage = (total_present / total_days * 100) if total_days > 0 else 0

    # تخزين تفاصيل التقرير في الجلسة لإعادة التوجيه
    session['report_details'] = {
        'student': student,
        'student_id': student_id,
        'start_date': start_date_str,
        'end_date': end_date_str,
        'report_data': report_data,
        'total_present': total_present,
        'total_absent': total_absent,
        'total_late': total_late,
        'total_excused': total_excused,
        'total_days': total_days,
        'attendance_percentage': attendance_percentage
    }

    flash('تم إنشاء تقرير الحضور بنجاح.', 'success')
    # إعادة التوجيه إلى صفحة التقارير لعرض النتائج مع الحفاظ على الفلاتر
    return redirect(url_for('student_reports', **request.args))

@app.route('/generate_teacher_report', methods=['POST'])
def generate_teacher_report():
    teacher_id = request.form.get('teacher_id')
    start_date_str = request.form.get('start_date')
    end_date_str = request.form.get('end_date')

    if not teacher_id or not start_date_str or not end_date_str:
        flash('يرجى تحديد المعلم وتواريخ البدء والانتهاء.', 'error')
        return redirect(url_for('teacher_reports'))

    try:
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
    except ValueError:
        flash('تنسيق التاريخ غير صالح. يرجى استخدام YYYY-MM-DD.', 'error')
        return redirect(url_for('teacher_reports'))

    teachers_list = load_data(TEACHERS_FILE)
    teacher = next((t for t in teachers_list if t['id'] == teacher_id), None)
    if not teacher:
        flash('المعلم المحدد غير موجود.', 'error')
        return redirect(url_for('teacher_reports'))

    teachers_attendance_data = load_data(TEACHERS_ATTENDANCE_FILE)
    report_data = []
    total_present = 0
    total_absent = 0
    total_late = 0
    total_excused = 0
    total_days = 0

    current_date = start_date
    while current_date <= end_date:
        date_str = current_date.strftime('%Y-%m-%d')
        day_attendance = teachers_attendance_data.get(date_str, {})
        status = day_attendance.get(teacher_id, 'لم يسجل')

        report_data.append({
            'date': date_str,
            'status': status
        })

        if status == 'present':
            total_present += 1
        elif status == 'absent':
            total_absent += 1
        elif status == 'late':
            total_late += 1
        elif status == 'excused':
            total_excused += 1

        # نعد الأيام التي تم تسجيلها فقط
        if status in ['present', 'absent', 'late', 'excused']:
            total_days += 1

        current_date += timedelta(days=1)

    # حساب النسبة المئوية للحضور (تجنب القسمة على صفر)
    attendance_percentage = (total_present / total_days * 100) if total_days > 0 else 0

    # تخزين تفاصيل التقرير في الجلسة لإعادة التوجيه
    session['teacher_report_details'] = {
        'teacher': teacher,
        'teacher_id': teacher_id,
        'start_date': start_date_str,
        'end_date': end_date_str,
        'report_data': report_data,
        'total_present': total_present,
        'total_absent': total_absent,
        'total_late': total_late,
        'total_excused': total_excused,
        'total_days': total_days,
        'attendance_percentage': attendance_percentage
    }

    flash('تم إنشاء تقرير حضور المعلم بنجاح.', 'success')
    return redirect(url_for('teacher_reports'))


# --- Behavioral Violations Routes ---
@app.route('/behavioral_violations')
def behavioral_violations():
    violations_raw = load_data(VIOLATIONS_FILE)
    students_dict = load_data(STUDENTS_FILE)
    # إنشاء قاموس للبحث السريع عن أسماء الطلاب بواسطة ID
    # المرور على قيم القاموس (التي هي قواميس تفاصيل الطلاب)
    student_names = {student_id: student_data['name'] for student_id, student_data in students_dict.items()}
    # إضافة اسم الطالب لكل مخالفة
    violations = []
    for v in violations_raw:
        v['student_name'] = student_names.get(v.get('student_id'), 'طالب غير معروف')
        violations.append(v)
    return render_template('behavioral_violations.html', violations=violations)

@app.route('/add_violation', methods=['GET', 'POST'])
def add_violation():
    if request.method == 'POST':
        violations = load_data(VIOLATIONS_FILE)
        student_id = request.form.get('student_id')
        if not student_id:
            flash('يرجى تحديد طالب أولاً.', 'error')
            return render_template('add_violation.html') # إعادة عرض النموذج مع رسالة الخطأ

        new_violation = {
            'id': str(len(violations) + 1), # طريقة بسيطة لتوليد ID
            'student_id': student_id,
            'description': request.form['violation_description'],
            'date': request.form['violation_date'],
            'degree': request.form.get('violation_degree', ''), # إضافة درجة المخالفة
            'action_taken': request.form.get('action_taken', ''), # حقل اختياري
            'behavioral_violation': request.form.get('behavioral_violation', '') # Get behavioral violation description
        }
        violations.append(new_violation)
        save_data(VIOLATIONS_FILE, violations)
        flash('تمت إضافة المخالفة السلوكية بنجاح!', 'success')
        return redirect(url_for('behavioral_violations'))

    # في حالة GET، فقط اعرض النموذج
    return render_template('add_violation.html')

@app.route('/delete_violation/<violation_id>', methods=['POST'])
def delete_violation(violation_id):
    violations = load_data(VIOLATIONS_FILE)
    violations = [v for v in violations if v.get('id') != violation_id]
    save_data(VIOLATIONS_FILE, violations)
    flash('تم حذف المخالفة بنجاح!', 'success')
    return redirect(url_for('behavioral_violations'))

# --- Settings Routes ---
@app.route('/settings', methods=['GET', 'POST'])
def settings():
    settings_data = load_data(SETTINGS_FILE)
    # التأكد من وجود مفتاح notification_methods وقيمه الافتراضية إذا لم يكن موجودًا
    if 'notification_methods' not in settings_data or not isinstance(settings_data.get('notification_methods'), dict):
        settings_data['notification_methods'] = {
            "app": True,
            "email": False,
            "whatsapp": False,
            "sms": False
        }

    if request.method == 'POST':
        flash_message = None
        action = request.form.get('action') # لتحديد أي نموذج تم إرساله

        if action == 'save_general_settings':
            settings_data['school_name'] = request.form['school_name']
            settings_data['education_department'] = request.form['education_department']
            settings_data['academic_year'] = request.form['academic_year']
            settings_data['semester'] = request.form['semester']

            notifications_phone = request.form.get('notifications_phone', '').strip()
            if notifications_phone:
                #  إزالة الصفر الأول إذا كان موجودًا وتأكد من وجود رمز الدولة
                if notifications_phone.startswith('0'):
                    notifications_phone = '966' + notifications_phone[1:]
                elif not notifications_phone.startswith('966') and not notifications_phone.startswith('+966'):
                    notifications_phone = '966' + notifications_phone
                if not notifications_phone.startswith('+'): # التأكد من وجود علامة +
                    notifications_phone = '+' + notifications_phone
                settings_data['notifications_phone'] = notifications_phone
            elif 'notifications_phone' in settings_data:
                # إذا كان الحقل فارغًا، قم بإزالة الرقم من الإعدادات إذا كان موجودًا
                del settings_data['notifications_phone']

            # تحديث إعداد استخدام رقم الهاتف للإرسال
            settings_data['use_settings_phone_for_sending'] = 'use_settings_phone_for_sending' in request.form

            if 'logo' in request.files and request.files['logo'].filename:
                logo_file = request.files['logo']
                if logo_file and allowed_file(logo_file.filename):
                    filename = secure_filename(logo_file.filename)
                    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
                    filename = f"{timestamp}_{filename}"
                    if not os.path.exists(UPLOAD_FOLDER):
                        os.makedirs(UPLOAD_FOLDER)
                    file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                    logo_file.save(file_path)
                    if settings_data.get('logo_path') and os.path.exists(os.path.join('static', settings_data['logo_path'])):
                        try:
                            os.remove(os.path.join('static', settings_data['logo_path']))
                        except Exception as e:
                            print(f"Error deleting old logo: {e}") # Log error for debugging
                            pass
                    settings_data['logo_path'] = 'uploads/' + filename
                else:
                    flash('صيغة الملف غير مدعومة. يرجى استخدام JPG أو PNG أو GIF.', 'danger')
            flash_message = 'تم حفظ إعدادات المدرسة بنجاح!'
        elif action == 'save_notification_settings':
            # التأكد من أن notification_methods هو قاموس قبل التعديل
            if not isinstance(settings_data.get('notification_methods'), dict):
                 settings_data['notification_methods'] = {}
            settings_data['notification_methods']['app'] = 'notification_app' in request.form
            settings_data['notification_methods']['email'] = 'notification_email' in request.form
            settings_data['notification_methods']['whatsapp'] = 'notification_whatsapp' in request.form
            settings_data['notification_methods']['sms'] = 'notification_sms' in request.form
            flash_message = 'تم حفظ إعدادات الإشعارات بنجاح!'

        if flash_message:
            save_data(SETTINGS_FILE, settings_data)
            flash(flash_message, 'success')
        else:
            # هذا الشرط يعني أن الطلب POST ولكن لم يتم التعرف على أي من النماذج المتوقعة
            flash('تم استلام الطلب، ولكن لم يتم إجراء أي تغييرات أو لم يتم التعرف على الإجراء.', 'warning')

        return redirect(url_for('settings'))

    return render_template('settings.html', settings=settings_data)

@app.route('/delete_logo', methods=['POST'])
def delete_logo():
    settings_data = load_data(SETTINGS_FILE)

    # حذف الشعار إذا وجد
    if settings_data.get('logo_path') and os.path.exists(os.path.join('static', settings_data['logo_path'])):
        try:
            os.remove(os.path.join('static', settings_data['logo_path']))
            settings_data['logo_path'] = ''
            save_data(SETTINGS_FILE, settings_data)
            flash('تم حذف الشعار بنجاح!', 'success')
        except Exception as e:
            flash(f'حدث خطأ أثناء حذف الشعار: {str(e)}', 'danger')
    else:
        flash('الشعار غير موجود!', 'warning')

    return redirect(url_for('settings'))

# --- API Routes ---
# --- Student Permission Routes ---
@app.route('/student_permission', methods=['GET', 'POST'])
def student_permission():
    # تحميل بيانات الطلاب
    students_dict = load_data(STUDENTS_FILE)

    # إنشاء ملف جديد للاستئذان إذا لم يكن موجودًا
    PERMISSIONS_FILE = os.path.join(DATA_DIR, 'student_permissions.json')
    permissions = load_data(PERMISSIONS_FILE) if os.path.exists(PERMISSIONS_FILE) else []

    if request.method == 'POST':
        # معالجة إضافة استئذان جديد
        student_id = request.form.get('student_id')
        permission_date = request.form.get('permission_date')
        exit_time = request.form.get('exit_time')
        expected_return_time = request.form.get('expected_return_time', '')
        reason = request.form.get('permission_reason')

        # استخدام رقم الاتصال من سجل الطالب إذا كان موجوداً، وإلا استخدام الرقم المدخل
        student = students_dict.get(student_id, {})
        if student and student.get('contact'):
            contact = student.get('contact')
        else:
            contact = '+966' + request.form.get('permission_contact', '')

        notes = request.form.get('permission_notes', '')
        parent_notified = 'parent_notified' in request.form

        # معلومات الطالب تم الحصول عليها سابقاً

        # معالجة معلومات المستأذن
        different_requester = 'different_requester' in request.form
        requester_name = request.form.get('requester_name', '') if different_requester else ''
        requester_relation = request.form.get('requester_relation', '') if different_requester else ''
        requester_contact = request.form.get('requester_contact', '') if different_requester else ''

        if requester_contact and not requester_contact.startswith('+966'):
            requester_contact = '+966' + requester_contact

        # إنشاء استئذان جديد
        new_permission = {
            'id': str(uuid.uuid4()),
            'student_id': student_id,
            'student_name': student.get('name', ''),
            'grade': student.get('grade', ''),
            'classroom': student.get('classroom', ''),
            'date': permission_date,
            'exit_time': exit_time,
            'expected_return_time': expected_return_time,
            'return_time': '',
            'reason': reason,
            'contact': contact,
            'notes': notes,
            'parent_notified': parent_notified,
            'different_requester': different_requester,
            'requester_name': requester_name,
            'requester_relation': requester_relation,
            'requester_contact': requester_contact,
            'status': 'pending',
            'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        permissions.append(new_permission)
        save_data(PERMISSIONS_FILE, permissions)
        flash('تم تسجيل طلب الاستئذان بنجاح!', 'success')
        return redirect(url_for('student_permission'))

    # معالجة GET لعرض الصفحة
    # الحصول على معايير البحث والتصفية
    search_query = request.args.get('search_query', '').lower()
    filter_date = request.args.get('filter_date', '')
    filter_status = request.args.get('filter_status', '')

    # تصفية طلبات الاستئذان
    filtered_permissions = []
    for permission in permissions:
        matches_search = (search_query in permission.get('student_name', '').lower() or
                         search_query in permission.get('student_id', '').lower())
        matches_date = (not filter_date or permission.get('date') == filter_date)
        matches_status = (not filter_status or permission.get('status') == filter_status)

        if matches_search and matches_date and matches_status:
            filtered_permissions.append(permission)

    # ترتيب طلبات الاستئذان حسب التاريخ (الأحدث أولاً)
    filtered_permissions.sort(key=lambda x: x.get('date', ''), reverse=True)

    # تحويل قاموس الطلاب إلى قائمة مع إضافة المعرف لكل طالب
    students_list = []
    for student_id, student_data in students_dict.items():
        student = student_data.copy()
        student['id'] = student_id
        students_list.append(student)

    return render_template('student_permission.html',
                          permissions=filtered_permissions,
                          students=students_list,
                          today_date=datetime.now().strftime('%Y-%m-%d'))

@app.route('/update_permission', methods=['POST'])
def update_permission():
    PERMISSIONS_FILE = os.path.join(DATA_DIR, 'student_permissions.json')
    permissions = load_data(PERMISSIONS_FILE)

    permission_id = request.form.get('permission_id')
    action = request.form.get('action')

    for permission in permissions:
        if permission.get('id') == permission_id:
            if action == 'approve':
                permission['status'] = 'approved'
                flash('تمت الموافقة على طلب الاستئذان!', 'success')
            elif action == 'reject':
                permission['status'] = 'rejected'
                flash('تم رفض طلب الاستئذان!', 'danger')
            elif action == 'return':
                permission['status'] = 'completed'
                permission['return_time'] = datetime.now().strftime('%H:%M')
                flash('تم تسجيل عودة الطالب بنجاح!', 'success')
            elif action == 'delete':
                try:
                    # حذف الاستئذان من القائمة
                    permissions.remove(permission)
                    flash('تم حذف طلب الاستئذان بنجاح!', 'success')
                    # حفظ التغييرات مباشرة
                    save_data(PERMISSIONS_FILE, permissions)
                except Exception as e:
                    flash(f'حدث خطأ أثناء محاولة حذف الاستئذان: {str(e)}', 'danger')
                # الخروج من الحلقة بعد الحذف
                return redirect(url_for('student_permission'))
            elif action == 'update':
                # تحديث بيانات الاستئذان
                student_id = request.form.get('student_id')

                # الحصول على معلومات الطالب المحدثة
                students_dict = load_data(STUDENTS_FILE)
                student = students_dict.get(student_id, {})

                # تحديث معلومات الطالب
                permission['student_id'] = student_id
                permission['student_name'] = student.get('name', permission.get('student_name', ''))
                permission['grade'] = student.get('grade', permission.get('grade', ''))
                permission['classroom'] = student.get('classroom', permission.get('classroom', ''))

                # تحديث بقية البيانات
                permission['date'] = request.form.get('date')
                permission['exit_time'] = request.form.get('exit_time')
                permission['expected_return_time'] = request.form.get('expected_return_time', '')
                permission['return_time'] = request.form.get('return_time', '')
                permission['reason'] = request.form.get('reason')

                # معالجة رقم الاتصال
                contact = request.form.get('contact', '')
                if contact and not contact.startswith('+966'):
                    contact = '+966' + contact
                permission['contact'] = contact

                # معالجة معلومات المستأذن
                different_requester = 'different_requester' in request.form
                permission['different_requester'] = different_requester

                if different_requester:
                    permission['requester_name'] = request.form.get('requester_name', '')
                    permission['requester_relation'] = request.form.get('requester_relation', '')
                    requester_contact = request.form.get('requester_contact', '')
                    if requester_contact and not requester_contact.startswith('+966'):
                        requester_contact = '+966' + requester_contact
                    permission['requester_contact'] = requester_contact
                else:
                    permission['requester_name'] = ''
                    permission['requester_relation'] = ''
                    permission['requester_contact'] = ''

                permission['notes'] = request.form.get('notes', '')
                permission['parent_notified'] = 'parent_notified' in request.form
                permission['status'] = request.form.get('status')

                # إذا تم تغيير الحالة إلى مكتمل وكان وقت العودة فارغًا، قم بتعيينه تلقائيًا
                if permission['status'] == 'completed' and not permission['return_time']:
                    permission['return_time'] = datetime.now().strftime('%H:%M')

                flash('تم تحديث بيانات الاستئذان بنجاح!', 'success')
            else:
                flash('إجراء غير معروف!', 'danger')

            save_data(PERMISSIONS_FILE, permissions)
            break

    return redirect(url_for('student_permission'))

@app.route('/notifications', methods=['GET', 'POST'])
def notifications():
    # إنشاء ملف جديد للإشعارات إذا لم يكن موجودًا
    NOTIFICATIONS_FILE = os.path.join(DATA_DIR, 'notifications.json')
    notifications = load_data(NOTIFICATIONS_FILE) if os.path.exists(NOTIFICATIONS_FILE) else []

    # تحميل بيانات الغيابات والاستئذان والتأخر
    ATTENDANCE_FILE = os.path.join(DATA_DIR, 'attendance.json')
    PERMISSIONS_FILE = os.path.join(DATA_DIR, 'student_permissions.json')
    TARDINESS_FILE = os.path.join(DATA_DIR, 'student_tardiness.json')

    # تحميل بيانات الطلاب
    students_dict = load_data(STUDENTS_FILE)

    # تحميل بيانات الحضور والغياب
    attendance_records = load_data(ATTENDANCE_FILE) if os.path.exists(ATTENDANCE_FILE) else {}

    # تحميل بيانات الاستئذان
    permissions = load_data(PERMISSIONS_FILE) if os.path.exists(PERMISSIONS_FILE) else []

    # تحميل بيانات التأخر (إذا كان الملف موجودًا)
    tardiness_records = load_data(TARDINESS_FILE) if os.path.exists(TARDINESS_FILE) else []

    # تجهيز بيانات الغيابات
    absences = []
    # attendance_records هو قاموس يحتوي على التواريخ كمفاتيح
    for date, day_records in attendance_records.items():
        # day_records هو قاموس يحتوي على معرفات الطلاب كمفاتيح وحالة الحضور كقيم
        for student_id, status in day_records.items():
            if status == 'absent':
                student = students_dict.get(student_id, {})
                absences.append({
                    'id': f"{date}_{student_id}",  # إنشاء معرف فريد
                    'student_id': student_id,
                    'student_name': student.get('name', 'غير معروف'),
                    'grade': student.get('grade', ''),
                    'classroom': student.get('classroom', ''),
                    'date': date,
                    'type': 'absence',
                    'contact': student.get('contact', ''),
                    'parent_name': student.get('parent_name', '')
                })

    # تجهيز بيانات الاستئذان
    permission_records = []
    for permission in permissions:
        student = students_dict.get(permission.get('student_id'), {})
        permission_records.append({
            'id': permission.get('id'),
            'student_id': permission.get('student_id'),
            'student_name': permission.get('student_name', student.get('name', 'غير معروف')),
            'grade': permission.get('grade', student.get('grade', '')),
            'classroom': permission.get('classroom', student.get('classroom', '')),
            'date': permission.get('date'),
            'exit_time': permission.get('exit_time'),
            'return_time': permission.get('return_time', 'لم يعد بعد'),
            'reason': permission.get('reason', ''),
            'type': 'permission',
            'status': permission.get('status', 'pending'),
            'contact': permission.get('contact', student.get('contact', '')),
            'parent_name': student.get('parent_name', '')
        })

    # تجهيز بيانات التأخر (من نفس ملف الحضور)
    tardiness = []
    # استخدام نفس ملف الحضور للحصول على بيانات التأخر
    for date, day_records in attendance_records.items():
        for student_id, status in day_records.items():
            if status == 'late':
                student = students_dict.get(student_id, {})
                tardiness.append({
                    'id': f"{date}_{student_id}_late",  # إنشاء معرف فريد
                    'student_id': student_id,
                    'student_name': student.get('name', 'غير معروف'),
                    'grade': student.get('grade', ''),
                    'classroom': student.get('classroom', ''),
                    'date': date,
                    'time': '08:00',  # وقت افتراضي
                    'minutes_late': 15,  # قيمة افتراضية
                    'type': 'tardiness',
                    'contact': student.get('contact', ''),
                    'parent_name': student.get('parent_name', '')
                })

    # دمج جميع البيانات في قائمة واحدة
    all_records = absences + permission_records + tardiness

    # ترتيب السجلات حسب التاريخ (الأحدث أولاً)
    all_records.sort(key=lambda x: x.get('date', ''), reverse=True)

    if request.method == 'POST':
        # معالجة إضافة إشعار جديد
        notification_title = request.form.get('notification_title')
        notification_content = request.form.get('notification_content')
        notification_type = request.form.get('notification_type')
        notification_date = request.form.get('notification_date')
        notification_time = request.form.get('notification_time', '')
        notification_priority = request.form.get('notification_priority', 'normal')
        action = request.form.get('action', 'send')  # الإجراء المطلوب (إرسال، حفظ كمسودة، جدولة)

        # معالجة المستلمين
        recipients = request.form.getlist('notification_recipients')

        # معالجة طرق الإرسال
        send_sms = 'send_sms' in request.form
        send_email = 'send_email' in request.form
        send_whatsapp = 'send_whatsapp' in request.form
        send_app = 'send_app' in request.form

        # تحديد حالة الإشعار بناءً على الإجراء
        notification_status = 'sent'  # الحالة الافتراضية

        if action == 'draft':
            notification_status = 'draft'
        elif action == 'schedule':
            notification_status = 'scheduled'
            # التحقق من وجود وقت للجدولة
            if not notification_time:
                flash('يجب تحديد وقت الإرسال للجدولة!', 'danger')
                return redirect(url_for('notifications'))

        # إنشاء إشعار جديد
        new_notification = {
            'id': str(uuid.uuid4()),
            'title': notification_title,
            'content': notification_content,
            'type': notification_type,
            'date': notification_date,
            'time': notification_time,
            'priority': notification_priority,
            'recipients': recipients,
            'recipients_count': len(recipients) if recipients else 0,
            'send_methods': {
                'sms': send_sms,
                'email': send_email,
                'whatsapp': send_whatsapp,
                'app': send_app
            },
            'status': notification_status,
            'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        # إرسال الإشعارات عبر الوسائل المختلفة
        if recipients:
            # الحصول على معلومات المستلمين
            recipient_contacts = []

            if notification_type == 'parents':
                # الحصول على أرقام هواتف أولياء الأمور
                for recipient_id in recipients:
                    if recipient_id.startswith('parent_'):
                        student_id = recipient_id.replace('parent_', '')
                        student = students_dict.get(student_id, {})
                        if student and student.get('contact'):
                            recipient_contacts.append({
                                'name': f"ولي أمر {student.get('name', '')}",
                                'phone': student.get('contact'),
                                'student_name': student.get('name')
                            })

            # إرسال عبر واتساب
            if send_whatsapp and recipient_contacts:
                for contact in recipient_contacts:
                    phone = contact.get('phone', '')
                    if phone:
                        # الاحتفاظ برقم هاتف ولي الأمر الأصلي للإرسال إليه
                        recipient_phone = phone

                        # الحصول على رقم الإرسال من الإعدادات
                        settings_data = load_data(SETTINGS_FILE)
                        if settings_data.get('notifications_phone'):
                            # استخدام رقم الهاتف المحدد في الإعدادات للعرض فقط
                            sender_phone = settings_data.get('notifications_phone')
                        else:
                            # استخدام رقم افتراضي إذا لم يتم تحديد رقم في الإعدادات
                            sender_phone = "+966546477459"

                        # تنسيق رقم الهاتف لواتساب (إزالة '+' وإضافة رمز الدولة إذا لزم الأمر)
                        if phone.startswith('+'):
                            phone = phone[1:]  # إزالة علامة '+'
                        elif not phone.startswith('966'):
                            # إضافة رمز الدولة إذا لم يكن موجودًا
                            if phone.startswith('0'):
                                phone = '966' + phone[1:]
                            else:
                                phone = '966' + phone

                        # إنشاء رابط واتساب مع معلومات إضافية عن نوع السجل
                        record_type = ""
                        if "غياب" in notification_title:
                            record_type = "غياب"
                        elif "تأخر" in notification_title:
                            record_type = "تأخر"
                        elif "استئذان" in notification_title:
                            record_type = "استئذان"

                        # إضافة معلومات عن نوع السجل في الرسالة
                        whatsapp_message = f"{notification_title}\n\n{notification_content}"
                        if record_type:
                            whatsapp_message += f"\n\nنوع الإشعار: {record_type}"
                            whatsapp_message += f"\nتاريخ التسجيل: {notification_date}"

                        # تخزين الرسالة المفصلة في الإشعار للاستخدام لاحقًا
                        if 'whatsapp_links' not in new_notification:
                            new_notification['whatsapp_links'] = []



                        # تشفير الرسالة للاستخدام في URL
                        import urllib.parse
                        encoded_message = urllib.parse.quote(whatsapp_message)

                        # استخدام رقم ولي الأمر للإرسال إليه
                        whatsapp_url = f"https://wa.me/{recipient_phone}?text={encoded_message}"

                        # إضافة رابط واتساب إلى الإشعار
                        if 'whatsapp_links' not in new_notification:
                            new_notification['whatsapp_links'] = []

                        new_notification['whatsapp_links'].append({
                            'recipient_name': contact.get('name', 'ولي أمر'),
                            'student_name': contact.get('student_name', ''),
                            'phone': recipient_phone,  # رقم هاتف ولي الأمر للإرسال إليه
                            'sender_phone': sender_phone,  # رقم الهاتف المستخدم للإرسال
                            'url': whatsapp_url,
                            'message': whatsapp_message  # تخزين الرسالة المفصلة في الرابط
                        })

        # حفظ الإشعار في قاعدة البيانات
        notification_id = new_notification['id']
        notifications.append(new_notification)
        save_data(NOTIFICATIONS_FILE, notifications)

        # عرض رسالة مناسبة بناءً على الإجراء
        if action == 'draft':
            flash('تم حفظ الإشعار كمسودة بنجاح!', 'success')
            return redirect(url_for('notifications'))
        elif action == 'schedule':
            scheduled_time = f"{notification_date} {notification_time}"
            flash(f'تم جدولة الإشعار للإرسال في {scheduled_time} بنجاح!', 'success')
            return redirect(url_for('notifications'))
        else:  # إرسال الآن
            # إذا كان هناك روابط واتساب، قم بتخزينها في الجلسة لاستخدامها لاحقًا
            if send_whatsapp and new_notification.get('whatsapp_links'):
                session['whatsapp_links'] = new_notification.get('whatsapp_links')
                flash('تم إنشاء الإشعار بنجاح! يمكنك الآن إرسال الرسائل عبر واتساب.', 'success')
                # إعادة التوجيه إلى صفحة إرسال واتساب
                return redirect(url_for('send_whatsapp_messages', notification_id=notification_id))
            else:
                flash('تم إرسال الإشعار بنجاح!', 'success')
                return redirect(url_for('notifications'))

    # معالجة GET لعرض الصفحة
    # الحصول على معايير البحث والتصفية
    search_query = request.args.get('search_query', '').lower()
    filter_date = request.args.get('filter_date', '')
    filter_type = request.args.get('filter_type', '')

    # تصفية الإشعارات
    filtered_notifications = []
    for notification in notifications:
        matches_search = (search_query in notification.get('title', '').lower() or
                         search_query in notification.get('content', '').lower())
        matches_date = (not filter_date or notification.get('date') == filter_date)
        matches_type = (not filter_type or notification.get('type') == filter_type)

        if matches_search and matches_date and matches_type:
            filtered_notifications.append(notification)

    # ترتيب الإشعارات حسب التاريخ (الأحدث أولاً)
    filtered_notifications.sort(key=lambda x: x.get('created_at', ''), reverse=True)

    # تصفية سجلات الغياب والاستئذان والتأخر
    filtered_records = []
    for record in all_records:
        matches_search = (search_query in record.get('student_name', '').lower() or
                         search_query in record.get('student_id', '').lower())
        matches_date = (not filter_date or record.get('date') == filter_date)
        matches_type = (not filter_type or
                       (filter_type == 'absences' and record.get('type') == 'absence') or
                       (filter_type == 'permissions' and record.get('type') == 'permission') or
                       (filter_type == 'tardiness' and record.get('type') == 'tardiness'))

        if not filter_type or filter_type == 'all' or matches_type:
            if matches_search and matches_date:
                filtered_records.append(record)

    return render_template('notifications.html',
                          notifications=filtered_notifications,
                          records=filtered_records,
                          today_date=datetime.now().strftime('%Y-%m-%d'))

@app.route('/send_whatsapp_messages/<notification_id>')
def send_whatsapp_messages(notification_id):
    # التحقق من وجود روابط واتساب في الجلسة
    whatsapp_links = session.get('whatsapp_links', [])

    if not whatsapp_links:
        flash('لا توجد رسائل واتساب للإرسال.', 'warning')
        return redirect(url_for('notifications'))

    # الحصول على معلومات الإشعار
    NOTIFICATIONS_FILE = os.path.join(DATA_DIR, 'notifications.json')
    notifications = load_data(NOTIFICATIONS_FILE) if os.path.exists(NOTIFICATIONS_FILE) else []

    notification = None
    for n in notifications:
        if n.get('id') == notification_id:
            notification = n
            break

    if not notification:
        flash('لم يتم العثور على الإشعار المطلوب.', 'danger')
        return redirect(url_for('notifications'))

    # الحصول على إعدادات النظام
    settings_data = load_data(SETTINGS_FILE)

    return render_template('send_whatsapp.html',
                          notification=notification,
                          whatsapp_links=whatsapp_links,
                          settings=settings_data)

@app.route('/api/get_recipients')
def get_recipients():
    recipient_type = request.args.get('type', '')
    recipients = []

    if recipient_type == 'students':
        # الحصول على قائمة الطلاب
        students_dict = load_data(STUDENTS_FILE)
        for student_id, student_data in students_dict.items():
            recipients.append({
                'id': student_id,
                'name': student_data.get('name', '')
            })
    elif recipient_type == 'teachers':
        # الحصول على قائمة المعلمين (يمكن إضافة ملف للمعلمين لاحقًا)
        # هذا مجرد مثال
        recipients = [
            {'id': 'teacher1', 'name': 'المعلم الأول'},
            {'id': 'teacher2', 'name': 'المعلم الثاني'},
            {'id': 'teacher3', 'name': 'المعلم الثالث'}
        ]
    elif recipient_type == 'parents':
        # الحصول على قائمة أولياء الأمور من بيانات الطلاب
        students_dict = load_data(STUDENTS_FILE)
        parents_set = set()  # استخدام مجموعة لتجنب التكرار

        for student_id, student_data in students_dict.items():
            # استخدام اسم الطالب كاسم ولي الأمر إذا لم يكن موجودًا
            student_name = student_data.get('name', '')
            parent_contact = student_data.get('contact', '')

            if student_name and parent_contact:
                parent_id = f"parent_{student_id}"
                # إضافة ولي الأمر فقط إذا لم يكن موجودًا بالفعل
                parent_key = f"{student_name}_{parent_contact}"
                if parent_key not in parents_set:
                    parents_set.add(parent_key)
                    recipients.append({
                        'id': parent_id,
                        'name': f"ولي أمر {student_name}"
                    })

    return jsonify(recipients)

@app.route('/api/search_students')
def api_search_students():
    query = request.args.get('query', '').lower()
    students_dict = load_data(STUDENTS_FILE)
    matching_students = []
    # المرور على قاموس الطلاب (المفتاح هو ID والقيمة هي بيانات الطالب)
    for student_id, student_data in students_dict.items():
        if query in student_data.get('name', '').lower():
            # إنشاء قاموس بالصيغة المطلوبة (id, name, class)
            matching_students.append({
                'id': student_id,
                'name': student_data.get('name'),
                'class': student_data.get('classroom') # استخدام الفصل الدراسي
            })
    return jsonify(matching_students)

@app.route('/api/get_student_parent')
def get_student_parent():
    student_id = request.args.get('student_id', '')
    if not student_id:
        return jsonify([])

    students_dict = load_data(STUDENTS_FILE)
    student = students_dict.get(student_id, {})

    if not student:
        return jsonify([])

    # إنشاء بيانات ولي الأمر
    parent = {
        'id': f"parent_{student_id}",
        'name': f"ولي أمر {student.get('name', '')}"
    }

    return jsonify([parent])

@app.route('/api/send_whatsapp', methods=['POST'])
def send_whatsapp():
    data = request.json
    if not data:
        return jsonify({'success': False, 'error': 'بيانات غير صالحة'})

    try:
        # استخراج رقم الهاتف من الرابط أو من البيانات المرسلة
        phone = data.get('phone', '')

        # إذا لم يتم توفير رقم الهاتف، حاول استخراجه من الرابط
        if not phone and 'url' in data:
            url = data['url']
            # استخراج رقم الهاتف من رابط واتساب
            try:
                # تحليل الرابط للحصول على رقم الهاتف
                parsed_url = urllib.parse.urlparse(url)
                query_params = urllib.parse.parse_qs(parsed_url.query)

                if 'phone' in query_params:
                    phone = query_params['phone'][0]
                else:
                    # محاولة استخراج الرقم من المسار
                    path_parts = parsed_url.path.split('/')
                    for part in path_parts:
                        if part.isdigit() or (part.startswith('+') and part[1:].isdigit()):
                            phone = part
                            break
            except:
                pass

        # استخدام رقم الهاتف من الإعدادات إذا كان موجودًا وإذا تم طلب ذلك
        use_settings_phone = data.get('use_settings_phone', False)
        if use_settings_phone or not phone:
            settings_data = load_data(SETTINGS_FILE)
            if settings_data.get('notifications_phone'):
                phone = settings_data.get('notifications_phone')

        # التحقق من وجود رقم هاتف صالح
        if not phone:
            return jsonify({'success': False, 'error': 'لم يتم توفير رقم هاتف صالح ولم يتم تحديد رقم هاتف في الإعدادات'})

        # التأكد من أن رقم الهاتف يبدأ بـ +
        if not phone.startswith('+'):
            phone = '+' + phone

        # الحصول على الرسالة
        message = data.get('message', '')

        # الحصول على الوقت الحالي
        now = datetime.now()
        current_hour = now.hour
        current_minute = now.minute + 1  # إضافة دقيقة واحدة للسماح بتحميل واتساب

        # تعديل الساعة إذا تجاوزت الدقائق 59
        if current_minute >= 60:
            current_hour += 1
            current_minute %= 60

        # استخراج نوع السجل من الرسالة
        record_type = None
        if "غياب" in message:
            record_type = "غياب"
        elif "تأخر" in message:
            record_type = "تأخر"
        elif "استئذان" in message:
            record_type = "استئذان"

        # الحصول على تاريخ اليوم
        today_date = datetime.now().strftime('%Y-%m-%d')

        # محاولة إرسال الرسالة باستخدام الطريقة المباشرة أولاً
        try:
            # استخدام الطريقة المباشرة للإرسال التلقائي الكامل
            success = send_whatsapp_direct(phone, message, record_type, today_date)
            if success:
                return jsonify({
                    'success': True,
                    'message': 'تم إرسال الرسالة بنجاح باستخدام الطريقة المباشرة',
                    'method': 'direct',
                    'phone': phone
                })
        except Exception as e:
            print(f"فشل إرسال الرسالة باستخدام الطريقة المباشرة: {e}")

        # محاولة إرسال الرسالة باستخدام Selenium كخطة بديلة
        try:
            # استخدام Selenium للإرسال التلقائي الكامل
            success = send_whatsapp_selenium(phone, message, record_type, today_date)
            if success:
                return jsonify({
                    'success': True,
                    'message': 'تم إرسال الرسالة بنجاح باستخدام Selenium',
                    'method': 'selenium',
                    'phone': phone
                })
        except Exception as e:
            print(f"فشل إرسال الرسالة باستخدام Selenium: {e}")

        # إذا فشلت الطريقتان السابقتان، استخدم pywhatkit كخطة بديلة أخيرة
        send_whatsapp_auto(phone, message, record_type, today_date)

        return jsonify({
            'success': True,
            'message': 'تم إرسال الرسالة بنجاح',
            'phone': phone,
            'sent_at': f"{current_hour}:{current_minute:02d}"
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/send_all_whatsapp', methods=['POST'])
def send_all_whatsapp():
    data = request.json
    if not data or 'links' not in data:
        return jsonify({'success': False, 'error': 'بيانات غير صالحة'})

    try:
        # استخدام مكتبة pywhatkit لإرسال جميع رسائل واتساب
        sent_count = 0
        failed_count = 0
        results = []

        # الحصول على الوقت الحالي
        now = datetime.now()
        current_hour = now.hour
        current_minute = now.minute

        # إرسال الرسائل لكل مستلم بفاصل زمني
        for i, link in enumerate(data['links']):
            try:
                # استخراج رقم الهاتف والرسالة
                phone = ''

                # استخدام رقم هاتف ولي الأمر للإرسال إليه
                recipient_phone = link.get('phone', '')

                # الحصول على رقم الإرسال من الإعدادات
                settings_data = load_data(SETTINGS_FILE)
                if settings_data.get('notifications_phone'):
                    # استخدام رقم الهاتف المحدد في الإعدادات للإرسال
                    sender_phone = settings_data.get('notifications_phone')
                    # تعيين رقم الهاتف للإرسال
                    phone = recipient_phone  # استخدام رقم المستلم للإرسال إليه
                else:
                    # استخدام رقم افتراضي إذا لم يتم تحديد رقم في الإعدادات
                    sender_phone = "+966546477459"
                    phone = recipient_phone  # استخدام رقم المستلم للإرسال إليه

                # إذا لم يتم استخدام رقم الهاتف من الإعدادات، استخدم الرقم من البيانات المرسلة
                if not phone:
                    # محاولة استخراج رقم الهاتف من البيانات المرسلة
                    if 'phone' in link:
                        phone = link['phone']
                    # محاولة استخراج رقم الهاتف من الرابط
                    elif 'url' in link:
                        url = link['url']
                        try:
                            # تحليل الرابط للحصول على رقم الهاتف
                            parsed_url = urllib.parse.urlparse(url)
                            query_params = urllib.parse.parse_qs(parsed_url.query)

                            if 'phone' in query_params:
                                phone = query_params['phone'][0]
                            else:
                                # محاولة استخراج الرقم من المسار
                                path_parts = parsed_url.path.split('/')
                                for part in path_parts:
                                    if part.isdigit() or (part.startswith('+') and part[1:].isdigit()):
                                        phone = part
                                        break
                        except:
                            pass

                # التحقق من وجود رقم هاتف صالح
                if not phone:
                    results.append({
                        'index': link.get('index', i+1),
                        'status': 'failed',
                        'error': 'لم يتم توفير رقم هاتف صالح'
                    })
                    failed_count += 1
                    continue

                # التأكد من أن رقم الهاتف يبدأ بـ +
                if not phone.startswith('+'):
                    phone = '+' + phone

                # الحصول على الرسالة
                detailed_message = link.get('message')
                if not detailed_message:  # Checks for None or empty string
                    # استخدام الرسالة الافتراضية إذا كانت الرسالة فارغة
                    message = "إشعار من نظام إدارة المدرسة"
                else:
                    message = detailed_message

                # إضافة دقيقة واحدة لكل رسالة لتجنب التداخل
                send_minute = current_minute + i % 60
                send_hour = current_hour + (send_minute // 60)
                send_minute = send_minute % 60

                # استخراج نوع السجل من الرسالة
                record_type = None
                if "غياب" in message:
                    record_type = "غياب"
                elif "تأخر" in message:
                    record_type = "تأخر"
                elif "استئذان" in message:
                    record_type = "استئذان"

                # الحصول على تاريخ اليوم
                today_date = datetime.now().strftime('%Y-%m-%d')

                # محاولة إرسال الرسالة باستخدام الطريقة المباشرة أولاً
                try:
                    # استخدام الطريقة المباشرة للإرسال التلقائي الكامل
                    success = send_whatsapp_direct(phone, message, record_type, today_date)
                    if not success:
                        # إذا فشلت الطريقة المباشرة، استخدم Selenium
                        success = send_whatsapp_selenium(phone, message, record_type, today_date)
                        if not success:
                            # إذا فشلت الطريقتان السابقتان، استخدم pywhatkit كخطة بديلة أخيرة
                            send_whatsapp_auto(phone, message, record_type, today_date)
                except Exception as e:
                    print(f"خطأ في إرسال الرسالة: {e}")
                    # محاولة استخدام Selenium كخطة بديلة
                    try:
                        success = send_whatsapp_selenium(phone, message, record_type, today_date)
                        if not success:
                            # إذا فشلت Selenium، استخدم pywhatkit كخطة بديلة أخيرة
                            send_whatsapp_auto(phone, message, record_type, today_date)
                    except Exception as e2:
                        print(f"خطأ في إرسال الرسالة باستخدام Selenium: {e2}")
                        # استخدام pywhatkit كخطة بديلة أخيرة
                        send_whatsapp_auto(phone, message, record_type, today_date)

                # إضافة تأخير قصير بين الرسائل
                time.sleep(2)

                # تسجيل النجاح
                results.append({
                    'index': link.get('index', i+1),
                    'phone': phone,
                    'sender_phone': sender_phone,  # إضافة رقم الإرسال
                    'status': 'success',
                    'message': 'تم إرسال الرسالة بنجاح',
                    'sent_at': f"{send_hour}:{send_minute:02d}"
                })
                sent_count += 1

            except Exception as e:
                # تسجيل الفشل
                results.append({
                    'index': link.get('index', i+1),
                    'phone': phone if 'phone' in locals() else 'غير معروف',
                    'status': 'failed',
                    'error': str(e)
                })
                failed_count += 1

        # تحديث حالة الإشعار إذا تم توفير معرف الإشعار
        if 'notification_id' in data:
            notifications = load_data(NOTIFICATIONS_FILE)
            for i, notification in enumerate(notifications):
                if notification.get('id') == data['notification_id']:
                    notifications[i]['status'] = 'sent'
                    notifications[i]['sent_at'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    notifications[i]['sent_results'] = results
                    break
            save_data(NOTIFICATIONS_FILE, notifications)

        # إرجاع النتائج
        success = sent_count > 0
        message = f'تم إرسال {sent_count} رسالة بنجاح'
        if failed_count > 0:
            message += f' وفشل إرسال {failed_count} رسالة'

        return jsonify({
            'success': success,
            'message': message,
            'sent_count': sent_count,
            'failed_count': failed_count,
            'results': results
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/auto_send_whatsapp', methods=['POST'])
def auto_send_whatsapp():
    data = request.json
    if not data:
        return jsonify({'success': False, 'error': 'بيانات غير صالحة'})

    try:
        links = data.get('links', [])
        notification_id = data.get('notification_id')
        use_settings_phone = data.get('use_settings_phone', False)

        if not links:
            return jsonify({'success': False, 'error': 'لا توجد رسائل للإرسال'})

        # الحصول على رقم الهاتف من الإعدادات إذا كان مطلوباً
        settings_phone = None
        if use_settings_phone:
            settings_data = load_data(SETTINGS_FILE)
            if settings_data.get('notifications_phone'):
                settings_phone = settings_data.get('notifications_phone')

        # إرسال الرسائل
        sent_count = 0
        failed_count = 0
        results = []

        for i, link in enumerate(links):
            try:
                # استخراج رقم الهاتف والرسالة
                phone = link.get('phone', '')
                message = link.get('message', 'رسالة من نظام إدارة المدرسة')

                # استخدام رقم الهاتف من الإعدادات إذا كان مطلوباً
                if use_settings_phone and settings_phone:
                    phone = settings_phone

                # التحقق من وجود رقم هاتف صالح
                if not phone:
                    results.append({
                        'index': link.get('index', i+1),
                        'status': 'failed',
                        'error': 'لم يتم توفير رقم هاتف صالح'
                    })
                    failed_count += 1
                    continue

                # التأكد من أن رقم الهاتف يبدأ بـ +
                if not phone.startswith('+'):
                    phone = '+' + phone

                # استخراج نوع السجل من الرسالة
                record_type = None
                if "غياب" in message:
                    record_type = "غياب"
                elif "تأخر" in message:
                    record_type = "تأخر"
                elif "استئذان" in message:
                    record_type = "استئذان"

                # الحصول على تاريخ اليوم
                today_date = datetime.now().strftime('%Y-%m-%d')

                # محاولة إرسال الرسالة باستخدام الطريقة المباشرة أولاً
                try:
                    success = send_whatsapp_direct(phone, message, record_type, today_date)
                    if not success:
                        # إذا فشلت الطريقة المباشرة، استخدم Selenium
                        success = send_whatsapp_selenium(phone, message, record_type, today_date)
                        if not success:
                            # إذا فشلت الطريقتان السابقتان، استخدم pywhatkit كخطة بديلة أخيرة
                            send_whatsapp_auto(phone, message, record_type, today_date)
                except Exception as e:
                    print(f"خطأ في إرسال الرسالة: {e}")
                    # محاولة استخدام Selenium كخطة بديلة
                    try:
                        success = send_whatsapp_selenium(phone, message, record_type, today_date)
                        if not success:
                            # إذا فشلت Selenium، استخدم pywhatkit كخطة بديلة أخيرة
                            send_whatsapp_auto(phone, message, record_type, today_date)
                    except Exception as e2:
                        print(f"خطأ في إرسال الرسالة باستخدام Selenium: {e2}")
                        # استخدام pywhatkit كخطة بديلة أخيرة
                        send_whatsapp_auto(phone, message, record_type, today_date)

                # إضافة تأخير قصير بين الرسائل
                time.sleep(2)

                # تسجيل النجاح
                results.append({
                    'index': link.get('index', i+1),
                    'phone': phone,
                    'status': 'success',
                    'message': 'تم إرسال الرسالة بنجاح'
                })
                sent_count += 1

            except Exception as e:
                # تسجيل الفشل
                results.append({
                    'index': link.get('index', i+1),
                    'phone': phone if 'phone' in locals() else 'غير معروف',
                    'status': 'failed',
                    'error': str(e)
                })
                failed_count += 1

        # تحديث حالة الإشعار إذا تم توفير معرف الإشعار
        if notification_id:
            notifications = load_data(NOTIFICATIONS_FILE)
            for i, notification in enumerate(notifications):
                if notification.get('id') == notification_id:
                    notifications[i]['status'] = 'sent'
                    notifications[i]['sent_at'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    notifications[i]['sent_results'] = results
                    break
            save_data(NOTIFICATIONS_FILE, notifications)

        # إرجاع النتائج
        success = sent_count > 0
        message = f'تم إرسال {sent_count} رسالة بنجاح'
        if failed_count > 0:
            message += f' وفشل إرسال {failed_count} رسالة'

        return jsonify({
            'success': success,
            'message': message,
            'sent_count': sent_count,
            'failed_count': failed_count,
            'results': results
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})





if __name__ == '__main__':
    try:
        # تمكين وضع التصحيح لتسهيل التطوير
        print("Attempting to start Flask development server...")
        print("The application will be available at: http://127.0.0.1:5000")
        # استخدام host='0.0.0.0' للسماح بالوصول من أي جهاز على الشبكة المحلية
        app.run(debug=True, host='127.0.0.1', port=5000)
    except Exception as e:
        print(f"Error starting Flask server: {e}")
        input("Press Enter to exit...")