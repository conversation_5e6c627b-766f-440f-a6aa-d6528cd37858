{% extends 'base.html' %}

{% block title %}إعدادات النظام - نظام إدارة المدرسة{% endblock %}

{% block content %}
<h2><i class="bi bi-gear-fill me-2"></i>إعدادات النظام</h2>
<hr>

{% with messages = get_flashed_messages(with_categories=true) %}
  {% if messages %}
    {% for category, message in messages %}
      <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
        {{ message }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
      </div>
    {% endfor %}
  {% endif %}
{% endwith %}

<div class="row">
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="bi bi-building me-2"></i>معلومات المدرسة</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('settings') }}" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="school_name" class="form-label">اسم المدرسة</label>
                        <input type="text" class="form-control" id="school_name" name="school_name" value="{{ settings.school_name }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="education_department" class="form-label">إدارة التعليم</label>
                        <input type="text" class="form-control" id="education_department" name="education_department" value="{{ settings.education_department }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="academic_year" class="form-label">العام الدراسي</label>
                        <input type="text" class="form-control" id="academic_year" name="academic_year" value="{{ settings.academic_year }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="semester" class="form-label">الفصل الدراسي</label>
                        <select class="form-select" id="semester" name="semester" required>
                            <option value="الفصل الدراسي الأول" {% if settings.semester == 'الفصل الدراسي الأول' %}selected{% endif %}>الفصل الدراسي الأول</option>
                            <option value="الفصل الدراسي الثاني" {% if settings.semester == 'الفصل الدراسي الثاني' %}selected{% endif %}>الفصل الدراسي الثاني</option>
                            <option value="الفصل الدراسي الثالث" {% if settings.semester == 'الفصل الدراسي الثالث' %}selected{% endif %}>الفصل الدراسي الثالث</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="notifications_phone" class="form-label">رقم هاتف إرسال الإشعارات</label>
                        <div class="input-group">
                            <span class="input-group-text">+966</span>
                            <input type="text" class="form-control" id="notifications_phone" name="notifications_phone"
                                value="{{ settings.notifications_phone|default('') }}"
                                placeholder="5XXXXXXXX"
                                pattern="[0-9]{9}"
                                title="يرجى إدخال رقم هاتف صحيح (9 أرقام بدون الصفر الأول)">
                        </div>
                        <div class="form-text">أدخل رقم الهاتف الذي سيتم استخدامه لإرسال الإشعارات (بدون الصفر الأول)</div>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="use_settings_phone_for_sending" name="use_settings_phone_for_sending"
                            {% if settings.use_settings_phone_for_sending %}checked{% endif %}>
                        <label class="form-check-label" for="use_settings_phone_for_sending">
                            استخدام هذا الرقم للإرسال بدلاً من أرقام أولياء الأمور
                        </label>
                        <div class="form-text">عند تفعيل هذا الخيار، سيتم إرسال جميع الإشعارات من هذا الرقم بدلاً من إرسالها إلى أرقام أولياء الأمور</div>
                    </div>
                    <div class="mb-3">
                        <label for="logo" class="form-label">شعار الوزارة</label>
                        <input type="file" class="form-control" id="logo" name="logo" accept="image/*">
                        <div class="form-text">اختر ملف صورة بتنسيق JPG أو PNG أو GIF.</div>
                    </div>
                    <button type="submit" class="btn btn-primary" name="action" value="save_general_settings"><i class="bi bi-save me-1"></i> حفظ الإعدادات</button>
                </form>
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="bi bi-bell-fill me-2"></i>إعدادات الإشعارات</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('settings') }}">
                    <h6>طرق إرسال الإشعارات المفضلة:</h6>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="notification_app" name="notification_app" {% if settings.notification_methods.app %}checked{% endif %}>
                        <label class="form-check-label" for="notification_app">
                            <i class="bi bi-app-indicator me-1"></i>داخل التطبيق
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="notification_email" name="notification_email" {% if settings.notification_methods.email %}checked{% endif %}>
                        <label class="form-check-label" for="notification_email">
                            <i class="bi bi-envelope me-1"></i>بريد إلكتروني
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="notification_whatsapp" name="notification_whatsapp" {% if settings.notification_methods.whatsapp %}checked{% endif %}>
                        <label class="form-check-label" for="notification_whatsapp">
                            <i class="bi bi-whatsapp me-1" style="color: #25D366;"></i>واتساب
                        </label>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="notification_sms" name="notification_sms" {% if settings.notification_methods.sms %}checked{% endif %}>
                        <label class="form-check-label" for="notification_sms">
                            <i class="bi bi-chat-dots me-1"></i>رسالة نصية SMS
                        </label>
                    </div>
                    <button type="submit" class="btn btn-success" name="action" value="save_notification_settings"><i class="bi bi-save me-1"></i> حفظ إعدادات الإشعارات</button>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>الشعار الحالي</h5>
            </div>
            <div class="card-body text-center">
                {% if settings.logo_path %}
                    <img src="{{ url_for('static', filename=settings.logo_path) }}" alt="شعار الوزارة" class="img-fluid mb-3" style="max-height: 200px;">
                    <form method="POST" action="{{ url_for('delete_logo') }}" class="mt-2">
                        <button type="submit" class="btn btn-danger btn-sm"><i class="bi bi-trash me-1"></i> حذف الشعار</button>
                    </form>
                {% else %}
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i> لم يتم تحميل شعار بعد.
                    </div>
                {% endif %}
            </div>
        </div>

        <div class="card">
            <div class="card-header bg-secondary text-white">
                <h5 class="mb-0"><i class="bi bi-question-circle me-2"></i>مساعدة</h5>
            </div>
            <div class="card-body">
                <p>يمكنك تعديل معلومات المدرسة وإدارة التعليم من هذه الصفحة.</p>
                <p>سيتم استخدام هذه المعلومات في التقارير والطباعة.</p>
                <p>يجب أن يكون حجم الشعار مناسبًا (يفضل أقل من 1 ميجابايت).</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
