{% extends 'base.html' %}

{% block title %}تقارير حضور المعلمين - نظام إدارة المدرسة{% endblock %}

{% block content %}
<h2><i class="bi bi-file-earmark-text-fill me-2"></i>تقارير حضور المعلمين</h2>
<hr>

<!-- === نموذج إنشاء التقرير === -->
<form action="{{ url_for('generate_teacher_report') }}" method="POST" class="mb-4 p-3 border rounded bg-light">
    <div class="row g-3 align-items-end">
        <div class="col-md-4">
            <label for="teacher_id" class="form-label">اختر المعلم:</label>
            <select class="form-select" id="teacher_id" name="teacher_id" required>
                <option value="" disabled selected>-- اختر معلم --</option>
                {% for teacher in teachers %}
                    <option value="{{ teacher.id }}">{{ teacher.name }} - {{ teacher.specialization }}</option>
                {% else %}
                    <option value="" disabled>لا يوجد معلمين مسجلين</option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-3">
            <label for="start_date" class="form-label">تاريخ البدء:</label>
            <input type="date" class="form-control" id="start_date" name="start_date" required>
        </div>
        <div class="col-md-3">
            <label for="end_date" class="form-label">تاريخ الانتهاء:</label>
            <input type="date" class="form-control" id="end_date" name="end_date" required>
        </div>
        <div class="col-md-2">
            <button type="submit" class="btn btn-success w-100"><i class="bi bi-clipboard-data me-1"></i> إنشاء التقرير</button>
        </div>
    </div>
</form>
<!-- === نهاية نموذج إنشاء التقرير === -->

{% if report_details %}
<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0"><i class="bi bi-person-badge-fill me-2"></i>تقرير حضور المعلم: {{ report_details.teacher.name }}</h5>
    </div>
    <div class="card-body">
        <div class="row mb-3">
            <div class="col-md-6">
                <p><strong>المعلم:</strong> {{ report_details.teacher.name }}</p>
                <p><strong>التخصص:</strong> {{ report_details.teacher.specialization }}</p>
                <p><strong>معلومات الاتصال:</strong> {{ report_details.teacher.contact }}</p>
            </div>
            <div class="col-md-6">
                <p><strong>الفترة:</strong> من {{ report_details.start_date }} إلى {{ report_details.end_date }}</p>
                <p><strong>إجمالي أيام الحضور:</strong> {{ report_details.total_present }}</p>
                <p><strong>إجمالي أيام الغياب:</strong> {{ report_details.total_absent }}</p>
                <p><strong>نسبة الحضور:</strong> {{ "%.2f"|format(report_details.attendance_percentage) }}%</p>
            </div>
        </div>

        <h5 class="mb-3"><i class="bi bi-calendar3 me-2"></i>تفاصيل الحضور اليومية:</h5>
        <div class="table-responsive">
            <table class="table table-striped table-hover border">
                <thead class="table-dark">
                    <tr>
                        <th>التاريخ</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    {% for day in report_details.report_data %}
                    <tr>
                        <td>{{ day.date }}</td>
                        <td>
                            {% if day.status == 'present' %}
                                <span class="badge bg-success">حاضر</span>
                            {% elif day.status == 'absent' %}
                                <span class="badge bg-danger">غائب</span>
                            {% elif day.status == 'late' %}
                                <span class="badge bg-warning text-dark">متأخر</span>
                            {% elif day.status == 'excused' %}
                                <span class="badge bg-info text-dark">غائب بعذر</span>
                            {% else %}
                                <span class="badge bg-secondary">لم يسجل</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <div class="mt-3">
            <button class="btn btn-primary" onclick="window.print()"><i class="bi bi-printer-fill me-1"></i> طباعة التقرير</button>
        </div>
    </div>
</div>
{% endif %}

{% endblock %}
