{% extends 'base.html' %}

{% block title %}إضافة طالب جديد - نظام إدارة المدرسة{% endblock %}

{% block content %}
<h2><i class="bi bi-person-plus-fill me-2"></i>إضافة طالب جديد</h2>
<hr>
<form method="POST" action="{{ url_for('add_student') }}">
    <div class="mb-3">
        <label for="student_id" class="form-label">معرف الطالب</label>
        <div class="input-group">
            <input type="text" class="form-control" id="student_id" name="student_id" required pattern="^1\d{9}$" title="يجب أن يتكون المعرف من 10 أرقام ويبدأ بالرقم 1" maxlength="10">
            <div class="input-group-text">
                <input class="form-check-input mt-0" type="checkbox" id="override_id_validation" name="override_id_validation" aria-label="Checkbox for overriding ID validation">
                <label class="form-check-label ms-2 small" for="override_id_validation">استثناء</label>
            </div>
        </div>
    </div>
    <div class="mb-3">
        <label for="name" class="form-label">اسم الطالب (رباعي)</label>
        <div class="input-group">
            <input type="text" class="form-control" id="name" name="name" required title="يجب إدخال الاسم الرباعي. الكلمات 'بن'، 'ابو'، 'با' لا تُحتسب.">
            <div class="input-group-text">
                <input class="form-check-input mt-0" type="checkbox" id="override_name_validation" name="override_name_validation" aria-label="Checkbox for overriding name validation" {% if override_name_checked %}checked{% endif %}>
                <label class="form-check-label ms-2 small" for="override_name_validation">استثناء</label>
            </div>
        </div>
    </div>
    <div class="mb-3">
        <label for="grade" class="form-label">الصف</label>
        <select class="form-select" id="grade" name="grade" required>
            <option value="" disabled selected>اختر الصف...</option>
            <option value="الأول المتوسط">الأول المتوسط</option>
            <option value="الثاني المتوسط">الثاني المتوسط</option>
            <option value="الثالث المتوسط">الثالث المتوسط</option>
        </select>
    </div>
    <div class="mb-3">
        <label for="classroom" class="form-label">الفصل</label>
        <select class="form-select" id="classroom" name="classroom" required>
            <option value="" disabled selected>اختر الفصل...</option>
            <option value="1">1</option>
            <option value="2">2</option>
            <option value="3">3</option>
            <option value="4">4</option>
            <option value="5">5</option>
        </select>
    </div>
    <div class="mb-3">
        <label for="contact" class="form-label">رقم الجوال</label>
        <div class="input-group">
            <input type="tel" class="form-control" id="contact" name="contact_number" required pattern="^5\d{8}$" title="يجب إدخال 9 أرقام تبدأ بالرقم 5" placeholder="5xxxxxxxx" maxlength="9">
            <span class="input-group-text">+966</span>
        </div>
    </div>
    <div class="row">
        <div class="col-md-6 d-grid gap-2 d-md-flex justify-content-md-start">
            <button type="submit" class="btn btn-success"><i class="bi bi-check-circle-fill me-1"></i> إضافة الطالب</button>
            <a href="{{ url_for('list_students') }}" class="btn btn-secondary"><i class="bi bi-x-circle-fill me-1"></i> إلغاء</a>
        </div>
    </div>
</form>

<hr>
<h4><i class="bi bi-file-earmark-excel-fill me-2"></i>إضافة طلاب عن طريق ملف Excel</h4>
<form method="POST" action="{{ url_for('add_student') }}" enctype="multipart/form-data">
    <div class="mb-3">
        <label for="excel_file" class="form-label">اختر ملف Excel (.xlsx, .xls)</label>
        <input type="file" class="form-control" id="excel_file" name="excel_file" accept=".xlsx, .xls" required>
        <div class="form-text">يجب أن يحتوي الملف على الأعمدة التالية بالترتيب: معرف الطالب، اسم الطالب، الصف، الفصل، رقم الجوال (بدون +966).</div>
    </div>
    <div class="row">
        <div class="col-md-6 d-grid gap-2 d-md-flex justify-content-md-start">
            <button type="submit" class="btn btn-success"><i class="bi bi-upload me-1"></i> رفع وإضافة الطلاب</button>
        </div>
    </div>
</form>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const studentIdInput = document.getElementById('student_id');
    const overrideCheckbox = document.getElementById('override_id_validation');

    // Store original validation attributes
    const originalPattern = studentIdInput.getAttribute('pattern');
    const originalTitle = studentIdInput.getAttribute('title');
    const originalMaxLength = studentIdInput.getAttribute('maxlength');

    overrideCheckbox.addEventListener('change', function() {
        if (this.checked) {
            // Remove validation attributes
            studentIdInput.removeAttribute('pattern');
            studentIdInput.removeAttribute('title');
            studentIdInput.removeAttribute('maxlength');
            studentIdInput.required = true; // Keep it required, but without specific format
        } else {
            // Restore validation attributes
            studentIdInput.setAttribute('pattern', originalPattern);
            studentIdInput.setAttribute('title', originalTitle);
            studentIdInput.setAttribute('maxlength', originalMaxLength);
        }
        // Trigger validation check if needed, or rely on form submission
        // studentIdInput.reportValidity(); // Optional: show immediate feedback
    });
});

    // Validation for four-part name
    const nameInput = document.getElementById('name');
    const overrideNameCheckbox = document.getElementById('override_name_validation');
    const excludedWords = ['بن', 'ابو', 'با']; // الكلمات المستثناة

    function validateName() {
        const nameValue = nameInput.value.trim();
        if (overrideNameCheckbox.checked || !nameValue) {
            nameInput.setCustomValidity(''); // Clear validation if overridden or empty
            return;
        }
        const nameParts = nameValue.split(/\s+/); // Split by one or more spaces
        const validParts = nameParts.filter(part => part && !excludedWords.includes(part.toLowerCase()));

        if (validParts.length < 4) {
            nameInput.setCustomValidity('يجب أن يتكون الاسم من أربعة أجزاء على الأقل (باستثناء ' + excludedWords.join(', ') + ').');
        } else {
            nameInput.setCustomValidity(''); // Valid
        }
        // Optional: report validity immediately
        // nameInput.reportValidity();
    }

    nameInput.addEventListener('input', validateName);
    overrideNameCheckbox.addEventListener('change', validateName); // Re-validate on checkbox change

    // Ensure validation runs on form submission attempt
    const studentForm = nameInput.closest('form');
    if (studentForm) {
        studentForm.addEventListener('submit', function(event) {
            // Re-validate name on submit just in case
            validateName(); // Use the validation function
            if (!this.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            this.classList.add('was-validated');
        }, false);
    }

</script>

{% endblock %}