{% extends 'base.html' %}

{% block title %}المخالفات السلوكية{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>إدارة المخالفات السلوكية</h1>
        <a href="{{ url_for('add_violation') }}" class="btn btn-primary">إضافة مخالفة جديدة</a>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <table class="table table-striped table-bordered">
        <thead class="table-dark">
            <tr>
                <th>#</th>
                <th>اسم الطالب</th>
                <th>تاريخ المخالفة</th>
                <th>درجة المخالفة</th>
                <th>المخالفة السلوكية</th>
                <th>الوصف</th>
                <th>الإجراء المتخذ</th>
                <th>إجراءات</th>
            </tr>
        </thead>
        <tbody>
            {% for violation in violations %}
            <tr>
                <td>{{ loop.index }}</td>
                <td>{{ violation.student_name | default('غير متوفر', true) }}</td> {# عرض اسم الطالب أو رسالة بديلة #}
                <td>{{ violation.date }}</td>
                <td>{{ violation.get('degree', '-') }}</td> <!-- عرض درجة المخالفة -->
                <td>{{ violation.get('behavioral_violation', '-') }}</td>
                <td>{{ violation.description }}</td>
                <td>{{ violation.action_taken }}</td>
                <td>
                    <form action="{{ url_for('delete_violation', violation_id=violation.id) }}" method="POST" style="display:inline;">
                        <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('هل أنت متأكد من حذف هذه المخالفة؟');">حذف</button>
                    </form>
                    <!-- يمكنك إضافة زر تعديل هنا لاحقًا إذا لزم الأمر -->
                </td>
            </tr>
            {% else %}
            <tr>
                <td colspan="6" class="text-center">لا توجد مخالفات مسجلة حاليًا.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}