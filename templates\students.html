{% extends 'base.html' %}

{% block title %}قائمة الطلاب - نظام إدارة المدرسة{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-3">
    <h2><i class="bi bi-people-fill me-2"></i>قائمة الطلاب</h2>
    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
        <a href="{{ url_for('add_student') }}" class="btn btn-success"><i class="bi bi-plus-circle-fill me-1"></i>إضافة طالب جديد</a>
    </div>
</div>

<!-- Search and Filter Form -->
<form method="GET" action="{{ url_for('list_students') }}" class="mb-4 p-3 border rounded bg-light">
    <div class="row g-3">
        <div class="col-md-4">
            <label for="search_query" class="form-label">بحث (بالاسم أو المعرف):</label>
            <input type="text" class="form-control" id="search_query" name="search_query" value="{{ search_query or '' }}">
        </div>
        <div class="col-md-3">
            <label for="filter_grade" class="form-label">تصفية حسب الصف:</label>
            <select class="form-select" id="filter_grade" name="filter_grade">
                <option value="">الكل</option>
                {% for grade in unique_grades %}
                <option value="{{ grade }}" {% if filter_grade == grade %}selected{% endif %}>{{ grade }}</option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-3">
            <label for="filter_classroom" class="form-label">تصفية حسب الفصل:</label>
            <select class="form-select" id="filter_classroom" name="filter_classroom">
                <option value="">الكل</option>
                {% for classroom in unique_classrooms %}
                <option value="{{ classroom }}" {% if filter_classroom == classroom %}selected{% endif %}>{{ classroom }}</option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-2 d-flex align-items-end">
            <button type="submit" class="btn btn-primary w-100"><i class="bi bi-search me-1"></i>بحث / تصفية</button>
        </div>
    </div>
</form>

{% if students_to_display %}
<table class="table table-striped table-hover">
    <thead>
        <tr>
            <th scope="col">#</th>
            <th scope="col">المعرف</th>
            <th scope="col">الاسم</th>
            <th scope="col">الصف</th>
            <th scope="col">الفصل</th>
            <th scope="col">رقم الجوال</th>
            <th scope="col">إجراءات</th>
        </tr>
    </thead>
    <tbody>
        {% for student_id, student in students_to_display.items() %}
        <tr>
            <th scope="row">{{ loop.index }}</th>
            <td>{{ student_id }}</td>
            <td>{{ student.name }}</td>
            <td>{{ student.grade }}</td>
            <td>{{ student.classroom }}</td>
            <td>{{ student.contact }}</td>
            <td>
                <a href="{{ url_for('edit_student', student_id=student_id) }}" class="btn btn-sm btn-warning me-1"><i class="bi bi-pencil-square"></i> تعديل</a>
                <form action="{{ url_for('delete_student', student_id=student_id) }}" method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من رغبتك في حذف هذا الطالب؟ لا يمكن التراجع عن هذا الإجراء.');">
                    <button type="submit" class="btn btn-sm btn-danger"><i class="bi bi-trash-fill"></i> حذف</button>
                </form>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>
{% else %}
<div class="alert alert-info" role="alert">
    {% if search_query or filter_grade or filter_classroom %}
        لا توجد نتائج تطابق معايير البحث أو التصفية.
    {% else %}
        لا يوجد طلاب مسجلون حتى الآن. <a href="{{ url_for('add_student') }}" class="alert-link">قم بإضافة طالب جديد</a>.
    {% endif %}
</div>
{% endif %}
{% endblock %}

{% block scripts %}
{{ super() }} {# Include scripts from base template if any #}
<script>
    // عند تحميل الصفحة، قم بمسح حقل البحث
    document.addEventListener('DOMContentLoaded', function() {
        const searchQueryInput = document.getElementById('search_query');
        if (searchQueryInput) {
            // لا نمسح القيمة إذا كانت الصفحة قد تم تحميلها للتو مع قيمة بحث موجودة
            // فقط نمسحها عند إعادة التحميل أو التنقل العادي للصفحة
            // إذا أردت مسحها دائمًا بعد كل بحث، أزل الشرط التالي
            const urlParams = new URLSearchParams(window.location.search);
            if (!urlParams.has('search_query')) {
                 // أو إذا أردت مسحها دائماً، قم بإزالة هذا الشرط
                 // searchQueryInput.value = '';
            }
            // يمكنك إلغاء التعليق عن السطر التالي إذا أردت مسح الحقل دائماً
            // searchQueryInput.value = '';
        }
    });
</script>
{% endblock %}