{% extends 'base.html' %}

{% block title %}استئذان الطلاب - نظام إدارة المدرسة{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-3">
    <h2><i class="bi bi-door-open-fill me-2"></i>استئذان الطلاب</h2>
    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addPermissionModal">
            <i class="bi bi-plus-circle-fill me-1"></i>تسجيل استئذان جديد
        </button>
    </div>
</div>

<!-- نموذج البحث والتصفية -->
<form method="GET" action="{{ url_for('student_permission') }}" class="mb-4 p-3 border rounded bg-light">
    <div class="row g-3">
        <div class="col-md-4">
            <label for="search_query" class="form-label"><i class="bi bi-search me-1"></i>بحث (بالاسم أو المعرف):</label>
            <input type="text" class="form-control" id="search_query" name="search_query" value="{{ request.args.get('search_query', '') }}">
        </div>
        <div class="col-md-3">
            <label for="filter_date" class="form-label"><i class="bi bi-calendar-date me-1"></i>تصفية حسب التاريخ:</label>
            <input type="date" class="form-control" id="filter_date" name="filter_date" value="{{ request.args.get('filter_date', '') }}">
        </div>
        <div class="col-md-3">
            <label for="filter_status" class="form-label"><i class="bi bi-funnel me-1"></i>تصفية حسب الحالة:</label>
            <select class="form-select" id="filter_status" name="filter_status">
                <option value="">الكل</option>
                <option value="pending" {% if request.args.get('filter_status') == 'pending' %}selected{% endif %}>قيد الانتظار</option>
                <option value="approved" {% if request.args.get('filter_status') == 'approved' %}selected{% endif %}>تمت الموافقة</option>
                <option value="rejected" {% if request.args.get('filter_status') == 'rejected' %}selected{% endif %}>مرفوض</option>
                <option value="completed" {% if request.args.get('filter_status') == 'completed' %}selected{% endif %}>مكتمل (عاد الطالب)</option>
            </select>
        </div>
        <div class="col-md-2 d-flex align-items-end">
            <button type="submit" class="btn btn-primary w-100"><i class="bi bi-search me-1"></i>بحث / تصفية</button>
        </div>
    </div>
</form>

<!-- جدول طلبات الاستئذان -->
<div class="table-responsive">
    <table class="table table-striped table-hover">
        <thead class="table-dark">
            <tr>
                <th scope="col">#</th>
                <th scope="col">المعرف</th>
                <th scope="col">اسم الطالب</th>
                <th scope="col">الصف/الفصل</th>
                <th scope="col">تاريخ الاستئذان</th>
                <th scope="col">وقت الخروج</th>
                <th scope="col">وقت العودة</th>
                <th scope="col">سبب الاستئذان</th>
                <th scope="col">الحالة</th>
                <th scope="col">إجراءات</th>
            </tr>
        </thead>
        <tbody>
            {% if permissions %}
                {% for permission in permissions %}
                <tr>
                    <th scope="row">{{ loop.index }}</th>
                    <td>{{ permission.student_id }}</td>
                    <td>{{ permission.student_name }}</td>
                    <td>{{ permission.grade }}/{{ permission.classroom }}</td>
                    <td>{{ permission.date }}</td>
                    <td>{{ permission.exit_time }}</td>
                    <td>{{ permission.return_time if permission.return_time else 'لم يعد بعد' }}</td>
                    <td>{{ permission.reason }}</td>
                    <td>
                        {% if permission.status == 'pending' %}
                            <span class="badge bg-warning text-dark">قيد الانتظار</span>
                        {% elif permission.status == 'approved' %}
                            <span class="badge bg-success">تمت الموافقة</span>
                        {% elif permission.status == 'rejected' %}
                            <span class="badge bg-danger">مرفوض</span>
                        {% elif permission.status == 'completed' %}
                            <span class="badge bg-info">مكتمل</span>
                        {% endif %}
                    </td>
                    <td>
                        <div class="btn-group" role="group">
                            {% if permission.status == 'pending' %}
                                <button type="button" class="btn btn-sm btn-success" onclick="approvePermission('{{ permission.id }}')" title="الموافقة على الاستئذان">
                                    <i class="bi bi-check-circle"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-danger" onclick="rejectPermission('{{ permission.id }}')" title="رفض الاستئذان">
                                    <i class="bi bi-x-circle"></i>
                                </button>
                            {% elif permission.status == 'approved' and not permission.return_time %}
                                <button type="button" class="btn btn-sm btn-info" onclick="markAsReturned('{{ permission.id }}')" title="تسجيل عودة الطالب">
                                    <i class="bi bi-arrow-return-left"></i> تسجيل العودة
                                </button>
                            {% endif %}
                            <button type="button" class="btn btn-sm btn-warning" data-bs-toggle="modal" data-bs-target="#editPermissionModal" onclick="editPermission('{{ permission.id }}')" title="تعديل بيانات الاستئذان">
                                <i class="bi bi-pencil-square"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-danger" onclick="deletePermission('{{ permission.id }}')" title="حذف الاستئذان">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="10" class="text-center">لا توجد طلبات استئذان مسجلة</td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<!-- Modal إضافة استئذان جديد -->
<div class="modal fade" id="addPermissionModal" tabindex="-1" aria-labelledby="addPermissionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="addPermissionModalLabel"><i class="bi bi-plus-circle me-2"></i>تسجيل استئذان جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addPermissionForm" method="POST" action="{{ url_for('student_permission') }}">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="student_id" class="form-label">اختر الطالب:</label>
                            <select class="form-select" id="student_id" name="student_id" required>
                                <option value="" disabled selected>-- اختر طالب --</option>
                                {% for student in students %}
                                    <option value="{{ student.id }}">{{ student.name }} ({{ student.id }}) - {{ student.grade }}/{{ student.classroom }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="permission_date" class="form-label">تاريخ الاستئذان:</label>
                            <input type="date" class="form-control" id="permission_date" name="permission_date" required value="{{ today_date }}">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="exit_time" class="form-label">وقت الخروج:</label>
                            <input type="time" class="form-control" id="exit_time" name="exit_time" required>
                        </div>
                        <div class="col-md-6">
                            <label for="expected_return_time" class="form-label">وقت العودة المتوقع (اختياري):</label>
                            <input type="time" class="form-control" id="expected_return_time" name="expected_return_time">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="permission_reason" class="form-label">سبب الاستئذان:</label>
                        <textarea class="form-control" id="permission_reason" name="permission_reason" rows="3" required></textarea>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="permission_contact" class="form-label">رقم التواصل مع ولي الأمر:</label>
                            <div class="input-group">
                                <input type="tel" class="form-control" id="permission_contact" name="permission_contact" required pattern="^5\d{8}$" title="يجب إدخال 9 أرقام تبدأ بالرقم 5" placeholder="5xxxxxxxx" maxlength="9" readonly>
                                <span class="input-group-text">+966</span>
                            </div>
                            <div class="form-text">يتم استدعاء رقم ولي الأمر تلقائياً من سجل الطالب</div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="different_requester" name="different_requester">
                                <label class="form-check-label" for="different_requester">
                                    المستأذن شخص آخر غير ولي الأمر
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات المستأذن (تظهر عند اختيار شخص آخر غير ولي الأمر) -->
                    <div id="requester_info" class="row mb-3" style="display: none;">
                        <div class="col-md-4">
                            <label for="requester_name" class="form-label">اسم المستأذن:</label>
                            <input type="text" class="form-control" id="requester_name" name="requester_name">
                        </div>
                        <div class="col-md-4">
                            <label for="requester_relation" class="form-label">صلة القرابة:</label>
                            <select class="form-select" id="requester_relation" name="requester_relation">
                                <option value="" selected disabled>اختر صلة القرابة...</option>
                                <option value="أب">أب</option>
                                <option value="أم">أم</option>
                                <option value="أخ">أخ</option>
                                <option value="أخت">أخت</option>
                                <option value="عم">عم</option>
                                <option value="عمة">عمة</option>
                                <option value="خال">خال</option>
                                <option value="خالة">خالة</option>
                                <option value="جد">جد</option>
                                <option value="جدة">جدة</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="requester_contact" class="form-label">رقم جوال المستأذن:</label>
                            <div class="input-group">
                                <input type="tel" class="form-control" id="requester_contact" name="requester_contact" pattern="^5\d{8}$" title="يجب إدخال 9 أرقام تبدأ بالرقم 5" placeholder="5xxxxxxxx" maxlength="9">
                                <span class="input-group-text">+966</span>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="permission_notes" class="form-label">ملاحظات إضافية (اختياري):</label>
                        <textarea class="form-control" id="permission_notes" name="permission_notes" rows="2"></textarea>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="parent_notified" name="parent_notified">
                        <label class="form-check-label" for="parent_notified">
                            تم التواصل مع ولي الأمر والتأكد من الموافقة
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <div class="row w-100">
                    <div class="col-md-6 text-start">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><i class="bi bi-x-circle me-1"></i>إلغاء</button>
                        <button type="submit" form="addPermissionForm" class="btn btn-success"><i class="bi bi-check-circle me-1"></i>تسجيل الاستئذان</button>
                    </div>
                    <div class="col-md-6 text-end">
                        <button type="button" class="btn btn-info" id="printPermissionBtn" disabled><i class="bi bi-printer me-1"></i>طباعة التقرير</button>
                        <button type="button" class="btn btn-warning" id="sendNotificationBtn" disabled><i class="bi bi-bell me-1"></i>إرسال إشعار</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal تعديل استئذان -->
<div class="modal fade" id="editPermissionModal" tabindex="-1" aria-labelledby="editPermissionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-warning">
                <h5 class="modal-title" id="editPermissionModalLabel"><i class="bi bi-pencil-square me-2"></i>تعديل استئذان</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editPermissionForm" method="POST" action="{{ url_for('update_permission') }}">
                    <input type="hidden" id="edit_permission_id" name="permission_id">
                    <input type="hidden" name="action" value="update">

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="edit_student_id" class="form-label">الطالب:</label>
                            <input type="text" class="form-control" id="edit_student_name" readonly>
                            <input type="hidden" id="edit_student_id" name="student_id">
                        </div>
                        <div class="col-md-6">
                            <label for="edit_permission_date" class="form-label">تاريخ الاستئذان:</label>
                            <input type="date" class="form-control" id="edit_permission_date" name="date" required>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="edit_exit_time" class="form-label">وقت الخروج:</label>
                            <input type="time" class="form-control" id="edit_exit_time" name="exit_time" required>
                        </div>
                        <div class="col-md-6">
                            <label for="edit_expected_return_time" class="form-label">وقت العودة المتوقع (اختياري):</label>
                            <input type="time" class="form-control" id="edit_expected_return_time" name="expected_return_time">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="edit_return_time" class="form-label">وقت العودة الفعلي (اختياري):</label>
                            <input type="time" class="form-control" id="edit_return_time" name="return_time">
                        </div>
                        <div class="col-md-6">
                            <label for="edit_status" class="form-label">حالة الاستئذان:</label>
                            <select class="form-select" id="edit_status" name="status" required>
                                <option value="pending">قيد الانتظار</option>
                                <option value="approved">تمت الموافقة</option>
                                <option value="rejected">مرفوض</option>
                                <option value="completed">مكتمل (عاد الطالب)</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="edit_permission_reason" class="form-label">سبب الاستئذان:</label>
                        <textarea class="form-control" id="edit_permission_reason" name="reason" rows="3" required></textarea>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="edit_permission_contact" class="form-label">رقم التواصل مع ولي الأمر:</label>
                            <div class="input-group">
                                <input type="tel" class="form-control" id="edit_permission_contact" name="contact" required pattern="^5\d{8}$" title="يجب إدخال 9 أرقام تبدأ بالرقم 5" placeholder="5xxxxxxxx" maxlength="9">
                                <span class="input-group-text">+966</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="edit_different_requester" name="different_requester">
                                <label class="form-check-label" for="edit_different_requester">
                                    المستأذن شخص آخر غير ولي الأمر
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات المستأذن (تظهر عند اختيار شخص آخر غير ولي الأمر) -->
                    <div id="edit_requester_info" class="row mb-3" style="display: none;">
                        <div class="col-md-4">
                            <label for="edit_requester_name" class="form-label">اسم المستأذن:</label>
                            <input type="text" class="form-control" id="edit_requester_name" name="requester_name">
                        </div>
                        <div class="col-md-4">
                            <label for="edit_requester_relation" class="form-label">صلة القرابة:</label>
                            <select class="form-select" id="edit_requester_relation" name="requester_relation">
                                <option value="" selected disabled>اختر صلة القرابة...</option>
                                <option value="أب">أب</option>
                                <option value="أم">أم</option>
                                <option value="أخ">أخ</option>
                                <option value="أخت">أخت</option>
                                <option value="عم">عم</option>
                                <option value="عمة">عمة</option>
                                <option value="خال">خال</option>
                                <option value="خالة">خالة</option>
                                <option value="جد">جد</option>
                                <option value="جدة">جدة</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="edit_requester_contact" class="form-label">رقم جوال المستأذن:</label>
                            <div class="input-group">
                                <input type="tel" class="form-control" id="edit_requester_contact" name="requester_contact" pattern="^5\d{8}$" title="يجب إدخال 9 أرقام تبدأ بالرقم 5" placeholder="5xxxxxxxx" maxlength="9">
                                <span class="input-group-text">+966</span>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_permission_notes" class="form-label">ملاحظات إضافية (اختياري):</label>
                        <textarea class="form-control" id="edit_permission_notes" name="notes" rows="2"></textarea>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="edit_parent_notified" name="parent_notified">
                        <label class="form-check-label" for="edit_parent_notified">
                            تم التواصل مع ولي الأمر والتأكد من الموافقة
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <div class="row w-100">
                    <div class="col-md-6 text-start">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><i class="bi bi-x-circle me-1"></i>إلغاء</button>
                        <button type="submit" form="editPermissionForm" class="btn btn-warning"><i class="bi bi-check-circle me-1"></i>حفظ التعديلات</button>
                    </div>
                    <div class="col-md-6 text-end">
                        <button type="button" class="btn btn-info" id="editPrintPermissionBtn"><i class="bi bi-printer me-1"></i>طباعة التقرير</button>
                        <button type="button" class="btn btn-warning" id="editSendNotificationBtn"><i class="bi bi-bell me-1"></i>إرسال إشعار</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    // استدعاء رقم ولي الأمر عند اختيار الطالب وإدارة حقول المستأذن
    document.addEventListener('DOMContentLoaded', function() {
        const studentSelect = document.getElementById('student_id');
        const contactInput = document.getElementById('permission_contact');
        const differentRequesterCheckbox = document.getElementById('different_requester');
        const requesterInfoDiv = document.getElementById('requester_info');
        const printPermissionBtn = document.getElementById('printPermissionBtn');
        const sendNotificationBtn = document.getElementById('sendNotificationBtn');

        // نموذج التعديل
        const editDifferentRequesterCheckbox = document.getElementById('edit_different_requester');
        const editRequesterInfoDiv = document.getElementById('edit_requester_info');
        const editPrintPermissionBtn = document.getElementById('editPrintPermissionBtn');
        const editSendNotificationBtn = document.getElementById('editSendNotificationBtn');

        // قائمة بيانات الطلاب
        const students = [
            {% for student in students %}
                {
                    id: "{{ student.id }}",
                    name: "{{ student.name }}",
                    contact: "{{ student.contact|replace('+966', '') if student.contact else '' }}"
                }{% if not loop.last %},{% endif %}
            {% endfor %}
        ];

        // استدعاء رقم الجوال عند تغيير الطالب
        studentSelect.addEventListener('change', function() {
            const selectedStudentId = this.value;
            if (selectedStudentId) {
                const selectedStudent = students.find(student => student.id === selectedStudentId);
                if (selectedStudent && selectedStudent.contact) {
                    contactInput.value = selectedStudent.contact;
                } else {
                    contactInput.value = '';
                    contactInput.readOnly = false;
                }

                // تفعيل أزرار الطباعة والإشعار عند اختيار طالب
                printPermissionBtn.disabled = false;
                sendNotificationBtn.disabled = false;
            } else {
                contactInput.value = '';
                printPermissionBtn.disabled = true;
                sendNotificationBtn.disabled = true;
            }
        });

        // إظهار/إخفاء حقول المستأذن
        if (differentRequesterCheckbox) {
            differentRequesterCheckbox.addEventListener('change', function() {
                requesterInfoDiv.style.display = this.checked ? 'flex' : 'none';

                // جعل حقول المستأذن مطلوبة أو غير مطلوبة حسب الاختيار
                const requesterInputs = requesterInfoDiv.querySelectorAll('input, select');
                requesterInputs.forEach(input => {
                    input.required = this.checked;
                });
            });
        }

        // إظهار/إخفاء حقول المستأذن في نموذج التعديل
        if (editDifferentRequesterCheckbox) {
            editDifferentRequesterCheckbox.addEventListener('change', function() {
                editRequesterInfoDiv.style.display = this.checked ? 'flex' : 'none';

                // جعل حقول المستأذن مطلوبة أو غير مطلوبة حسب الاختيار
                const requesterInputs = editRequesterInfoDiv.querySelectorAll('input, select');
                requesterInputs.forEach(input => {
                    input.required = this.checked;
                });
            });
        }

        // طباعة تقرير الاستئذان
        if (printPermissionBtn) {
            printPermissionBtn.addEventListener('click', function() {
                // التحقق من اختيار طالب
                if (studentSelect.value) {
                    printPermissionReport();
                } else {
                    alert('الرجاء اختيار طالب أولاً');
                }
            });
        }

        // إرسال إشعار
        if (sendNotificationBtn) {
            sendNotificationBtn.addEventListener('click', function() {
                // التحقق من اختيار طالب
                if (studentSelect.value) {
                    sendPermissionNotification();
                } else {
                    alert('الرجاء اختيار طالب أولاً');
                }
            });
        }

        // طباعة تقرير الاستئذان (نموذج التعديل)
        if (editPrintPermissionBtn) {
            editPrintPermissionBtn.addEventListener('click', function() {
                printPermissionReport(document.getElementById('edit_permission_id').value);
            });
        }

        // إرسال إشعار (نموذج التعديل)
        if (editSendNotificationBtn) {
            editSendNotificationBtn.addEventListener('click', function() {
                sendPermissionNotification(document.getElementById('edit_permission_id').value);
            });
        }

        // وظيفة طباعة تقرير الاستئذان
        function printPermissionReport(permissionId) {
            alert('سيتم طباعة تقرير الاستئذان قريباً...');
            // يمكن تنفيذ طباعة التقرير هنا
        }

        // وظيفة إرسال إشعار
        function sendPermissionNotification(permissionId) {
            alert('سيتم إرسال إشعار قريباً...');
            // يمكن تنفيذ إرسال الإشعار هنا
        }
    });

    // وظائف JavaScript للتعامل مع طلبات الاستئذان
    function approvePermission(id) {
        if (confirm('هل أنت متأكد من الموافقة على هذا الاستئذان؟')) {
            try {
                // إرسال طلب للموافقة
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = "{{ url_for('update_permission') }}";
                form.style.display = 'none'; // إخفاء النموذج

                const permissionIdInput = document.createElement('input');
                permissionIdInput.type = 'hidden';
                permissionIdInput.name = 'permission_id';
                permissionIdInput.value = id;

                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = 'approve';

                form.appendChild(permissionIdInput);
                form.appendChild(actionInput);
                document.body.appendChild(form);
                console.log('Submitting approve form for permission ID: ' + id);
                form.submit();
            } catch (error) {
                console.error('Error approving permission:', error);
                alert('حدث خطأ أثناء محاولة الموافقة على الاستئذان: ' + error.message);
            }
        }
    }

    function rejectPermission(id) {
        if (confirm('هل أنت متأكد من رفض هذا الاستئذان؟')) {
            try {
                // إرسال طلب للرفض
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = "{{ url_for('update_permission') }}";
                form.style.display = 'none'; // إخفاء النموذج

                const permissionIdInput = document.createElement('input');
                permissionIdInput.type = 'hidden';
                permissionIdInput.name = 'permission_id';
                permissionIdInput.value = id;

                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = 'reject';

                form.appendChild(permissionIdInput);
                form.appendChild(actionInput);
                document.body.appendChild(form);
                console.log('Submitting reject form for permission ID: ' + id);
                form.submit();
            } catch (error) {
                console.error('Error rejecting permission:', error);
                alert('حدث خطأ أثناء محاولة رفض الاستئذان: ' + error.message);
            }
        }
    }

    function markAsReturned(id) {
        if (confirm('هل تريد تسجيل عودة الطالب؟')) {
            try {
                // إرسال طلب لتسجيل العودة
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = "{{ url_for('update_permission') }}";
                form.style.display = 'none'; // إخفاء النموذج

                const permissionIdInput = document.createElement('input');
                permissionIdInput.type = 'hidden';
                permissionIdInput.name = 'permission_id';
                permissionIdInput.value = id;

                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = 'return';

                form.appendChild(permissionIdInput);
                form.appendChild(actionInput);
                document.body.appendChild(form);
                console.log('Submitting return form for permission ID: ' + id);
                form.submit();
            } catch (error) {
                console.error('Error marking student as returned:', error);
                alert('حدث خطأ أثناء محاولة تسجيل عودة الطالب: ' + error.message);
            }
        }
    }

    function editPermission(id) {
        // البحث عن بيانات الاستئذان من القائمة المعروضة
        const permissions = [
            {% for permission in permissions %}
                {
                    id: "{{ permission.id }}",
                    student_id: "{{ permission.student_id }}",
                    student_name: "{{ permission.student_name }}",
                    date: "{{ permission.date }}",
                    exit_time: "{{ permission.exit_time }}",
                    expected_return_time: "{{ permission.expected_return_time }}",
                    return_time: "{{ permission.return_time }}",
                    reason: "{{ permission.reason }}",
                    contact: "{{ permission.contact|replace('+966', '') if permission.contact else '' }}",
                    notes: "{{ permission.notes }}",
                    parent_notified: {{ 'true' if permission.parent_notified else 'false' }},
                    status: "{{ permission.status }}"
                }{% if not loop.last %},{% endif %}
            {% endfor %}
        ];

        // البحث عن الاستئذان المطلوب
        const permission = permissions.find(p => p.id === id);
        if (permission) {
            // ملء النموذج بالبيانات
            document.getElementById('edit_permission_id').value = permission.id;
            document.getElementById('edit_student_id').value = permission.student_id;
            document.getElementById('edit_student_name').value = permission.student_name;
            document.getElementById('edit_permission_date').value = permission.date;
            document.getElementById('edit_exit_time').value = permission.exit_time;
            document.getElementById('edit_expected_return_time').value = permission.expected_return_time;
            document.getElementById('edit_return_time').value = permission.return_time;
            document.getElementById('edit_permission_reason').value = permission.reason;
            document.getElementById('edit_permission_contact').value = permission.contact;
            document.getElementById('edit_permission_notes').value = permission.notes;
            document.getElementById('edit_parent_notified').checked = permission.parent_notified;
            document.getElementById('edit_status').value = permission.status;

            // عرض النموذج
            const modal = new bootstrap.Modal(document.getElementById('editPermissionModal'));
            modal.show();
        } else {
            alert('لم يتم العثور على بيانات الاستئذان!');
        }
    }

    function deletePermission(id) {
        if (confirm('هل أنت متأكد من حذف هذا الاستئذان؟ لا يمكن التراجع عن هذا الإجراء.')) {
            try {
                // إرسال طلب للحذف
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = "{{ url_for('update_permission') }}";
                form.style.display = 'none'; // إخفاء النموذج

                const permissionIdInput = document.createElement('input');
                permissionIdInput.type = 'hidden';
                permissionIdInput.name = 'permission_id';
                permissionIdInput.value = id;

                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = 'delete';

                // إضافة عناصر النموذج
                form.appendChild(permissionIdInput);
                form.appendChild(actionInput);

                // إضافة النموذج للصفحة وإرساله
                document.body.appendChild(form);
                console.log('Submitting delete form for permission ID: ' + id);
                form.submit();
            } catch (error) {
                console.error('Error deleting permission:', error);
                alert('حدث خطأ أثناء محاولة حذف الاستئذان: ' + error.message);
            }
        }
    }


</script>
{% endblock %}
