{% extends 'base.html' %}

{% block title %}تسجيل الحضور والانصراف{% endblock %}

{% block content %}
<h2><i class="bi bi-calendar-check-fill me-2"></i>تسجيل الحضور والانصراف</h2>
<hr>

<!-- Search and Filter Form -->
<form method="get" action="{{ url_for('record_student_attendance') }}" class="mb-4 p-3 border rounded bg-light">
    <div class="row g-3 align-items-end">
        <div class="col-md-3">
            <label for="search_query" class="form-label">بحث (بالاسم أو المعرف):</label>
            <input type="text" class="form-control" id="search_query" name="search_query" value="{{ search_query }}">
        </div>
        <div class="col-md-2">
            <label for="filter_grade" class="form-label">الصف:</label>
            <select id="filter_grade" name="filter_grade" class="form-select">
                <option value="">الكل</option>
                {% for grade in unique_grades %}
                    <option value="{{ grade }}" {{ 'selected' if filter_grade == grade else '' }}>{{ grade }}</option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-2">
            <label for="filter_classroom" class="form-label">الفصل:</label>
            <select id="filter_classroom" name="filter_classroom" class="form-select">
                <option value="">الكل</option>
                {% for classroom in unique_classrooms %}
                    <option value="{{ classroom }}" {{ 'selected' if filter_classroom == classroom else '' }}>{{ classroom }}</option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-3">
            <label for="attendance_date" class="form-label">التاريخ:</label>
            <input type="date" id="attendance_date" name="attendance_date" class="form-control" value="{{ today_date }}">
        </div>
        <div class="col-md-2">
            <button type="submit" class="btn btn-primary w-100"><i class="bi bi-filter me-1"></i> تطبيق الفلتر</button>
        </div>
    </div>
</form>

<!-- Attendance Form -->
<form method="post" action="{{ url_for('record_student_attendance') }}">
    <input type="hidden" name="attendance_date" value="{{ today_date }}">

    {% if students_to_display %}
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h4>قائمة الطلاب لـ: {{ today_date }}
            {% if filter_grade %} - المرحلة: {{ filter_grade }} {% endif %}
            {% if filter_classroom %} - الفصل: {{ filter_classroom }} {% endif %}
        </h4>
        <button type="submit" class="btn btn-success"><i class="bi bi-check-circle-fill me-1"></i> حفظ الحضور</button>
    </div>

    <div class="table-responsive">
        <table class="table table-striped table-hover border">
            <thead class="table-dark">
                <tr>
                    <th><input type="checkbox" id="select-all-present"> حاضر</th>
                    <th><input type="checkbox" id="select-all-absent"> غائب</th>
                    <th><input type="checkbox" id="select-all-late"> متأخر</th>
                    <th><input type="checkbox" id="select-all-excused"> غائب بعذر</th>
                    <th>اسم الطالب</th>
                    <th>المرحلة</th>
                    <th>الفصل</th>
                </tr>
            </thead>
            <tbody>
                {% for student_id, student in students_to_display.items() %}
                <tr>
                    <td><input type="radio" name="status_{{ student_id }}" value="present" class="form-check-input present-radio" {{ 'checked' if today_attendance.get(student_id) == 'present' else '' }}></td>
                    <td><input type="radio" name="status_{{ student_id }}" value="absent" class="form-check-input absent-radio" {{ 'checked' if today_attendance.get(student_id) == 'absent' else '' }}></td>
                    <td><input type="radio" name="status_{{ student_id }}" value="late" class="form-check-input late-radio" {{ 'checked' if today_attendance.get(student_id) == 'late' else '' }}></td>
                    <td><input type="radio" name="status_{{ student_id }}" value="excused" class="form-check-input excused-radio" {{ 'checked' if today_attendance.get(student_id) == 'excused' else '' }}></td>
                    <td>{{ student.name }}</td>
                    <td>{{ student.grade }}</td>
                    <td>{{ student.classroom }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    <div class="mt-3 text-end">
        <button type="submit" class="btn btn-success"><i class="bi bi-check-circle-fill me-1"></i> حفظ الحضور</button>
    </div>
    {% else %}
        <div class="alert alert-info" role="alert">
            الرجاء تحديد تاريخ أو استخدام الفلاتر لعرض الطلاب.
        </div>
    {% endif %}
</form>
{% endblock %}

{% block scripts %}
{{ super() }} <!-- Include scripts from base template if any -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Function to handle 'Select All' checkboxes
    function setupSelectAll(masterCheckboxId, targetRadioClass) {
        const masterCheckbox = document.getElementById(masterCheckboxId);
        if (masterCheckbox) {
            masterCheckbox.addEventListener('change', function() {
                const radios = document.querySelectorAll('.' + targetRadioClass);
                radios.forEach(radio => {
                    radio.checked = this.checked;
                });
                // Uncheck other master checkboxes if this one is checked
                if (this.checked) {
                    const allMasterCheckboxes = ['select-all-present', 'select-all-absent', 'select-all-late', 'select-all-excused'];
                    allMasterCheckboxes.forEach(id => {
                        if (id !== masterCheckboxId) {
                            const otherMaster = document.getElementById(id);
                            if (otherMaster) otherMaster.checked = false;
                        }
                    });
                }
            });
        }
    }

    setupSelectAll('select-all-present', 'present-radio');
    setupSelectAll('select-all-absent', 'absent-radio');
    setupSelectAll('select-all-late', 'late-radio');
    setupSelectAll('select-all-excused', 'excused-radio');

    // Optional: Clear search field after successful save (if needed)
    {% if request.args.get('saved') == 'true' %}
        // You might want to clear or reset parts of the form
        // Example: document.getElementById('search_field_id').value = '';
    {% endif %}
});
</script>
{% endblock %}
