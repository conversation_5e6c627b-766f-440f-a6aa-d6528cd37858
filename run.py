"""
برنامج بسيط لتشغيل تطبيق نظام إدارة المدرسة
"""

import os
import sys
import subprocess
import webbrowser
import time

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    print("=" * 50)
    print("تشغيل نظام إدارة المدرسة")
    print("=" * 50)
    print("")
    
    # تشغيل التطبيق
    print("جاري تشغيل التطبيق...")
    
    try:
        # تشغيل التطبيق مباشرة
        subprocess.run([sys.executable, "app.py"], check=True)
    except subprocess.CalledProcessError:
        print("حدث خطأ أثناء تشغيل التطبيق.")
        input("اضغط أي مفتاح للخروج...")
    except KeyboardInterrupt:
        print("\nتم إيقاف التطبيق بواسطة المستخدم.")
    except Exception as e:
        print(f"حدث خطأ غير متوقع: {e}")
        input("اضغط أي مفتاح للخروج...")

if __name__ == "__main__":
    main()
