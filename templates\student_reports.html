{% extends 'base.html' %}

{% block title %}تقارير حضور الطلاب - نظام إدارة المدرسة{% endblock %}

{% block styles %}
{{ super() }}
<style>
    /* أنماط خاصة بالطباعة */
    @media print {
        /* إخفاء العناصر غير المطلوبة عند الطباعة */
        header, .sidebar, .top-banner, nav, form, .btn, footer, .no-print,
        #sidebar, .sidebar-header, .sidebar-nav, .sidebar-footer, .sidebar-toggle, .sidebar-wrapper {
            display: none !important;
            width: 0 !important;
            height: 0 !important;
            position: absolute !important;
            overflow: hidden !important;
            z-index: -1000 !important;
            opacity: 0 !important;
            visibility: hidden !important;
        }

        /* ضبط حجم الصفحة على A4 */
        @page {
            size: A4 portrait;
            margin: 1cm;
        }

        body {
            background-color: white !important;
            font-size: 12pt;
            color: black;
            margin: 0 !important;
            padding: 0 !important;
            width: 100% !important;
            height: 100% !important;
        }

        /* تنسيق التقرير للطباعة */
        .print-container {
            width: 100% !important;
            margin: 0 auto !important;
            padding: 0 !important;
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
        }

        /* إعادة تعيين تنسيق الصفحة بالكامل عند الطباعة */
        html, body {
            width: 100% !important;
            margin: 0 !important;
            padding: 0 !important;
            overflow: visible !important;
        }

        .main-content {
            margin: 0 !important;
            padding: 0 !important;
            width: 100% !important;
            left: 0 !important;
        }

        /* إظهار محتوى التقرير فقط */
        body * {
            visibility: hidden;
        }

        .print-container, .print-container * {
            visibility: visible;
        }

        .print-container {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            padding: 0;
            margin: 0;
        }

        /* إخفاء القائمة الجانبية تماماً */
        .sidebar, #sidebar, .sidebar-wrapper, .sidebar-toggle {
            display: none !important;
            width: 0 !important;
            height: 0 !important;
            margin: 0 !important;
            padding: 0 !important;
            position: absolute !important;
            left: -9999px !important;
        }

        .card {
            border: none !important;
            box-shadow: none !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        .card-header {
            background-color: white !important;
            color: black !important;
            border-bottom: 2px solid #000 !important;
            padding: 10px 0 !important;
            margin-bottom: 20px !important;
            text-align: center !important;
        }

        .card-body {
            padding: 0 !important;
            margin: 0 !important;
        }

        h1, h2, h3, h4, h5, h6 {
            margin-top: 0.5cm !important;
            margin-bottom: 0.5cm !important;
            page-break-after: avoid !important;
        }

        /* تنسيق الجدول للطباعة */
        .table {
            width: 100% !important;
            border-collapse: collapse !important;
            margin-bottom: 20px !important;
            page-break-inside: auto !important;
        }

        .table th {
            background-color: #f0f0f0 !important;
            color: black !important;
            border: 1px solid #000 !important;
            padding: 8px !important;
            font-weight: bold !important;
            text-align: center !important;
        }

        .table td {
            border: 1px solid #000 !important;
            padding: 8px !important;
            text-align: center !important;
        }

        .table tr {
            page-break-inside: avoid !important;
            page-break-after: auto !important;
        }

        thead {
            display: table-header-group !important;
        }

        tfoot {
            display: table-footer-group !important;
        }

        /* تنسيق الشارات (badges) للطباعة */
        .badge {
            padding: 5px 8px !important;
            border: 1px solid #000 !important;
            color: black !important;
            font-weight: bold !important;
            display: inline-block !important;
            width: 60px !important;
            text-align: center !important;
        }

        .bg-success {
            background-color: white !important;
            border-color: #28a745 !important;
            position: relative !important;
        }

        .bg-success:after {
            content: "✓" !important;
            position: absolute !important;
            right: 3px !important;
            top: 2px !important;
        }

        .bg-danger {
            background-color: white !important;
            border-color: #dc3545 !important;
            position: relative !important;
        }

        .bg-danger:after {
            content: "✗" !important;
            position: absolute !important;
            right: 3px !important;
            top: 2px !important;
        }

        .bg-warning {
            background-color: white !important;
            border-color: #ffc107 !important;
            position: relative !important;
        }

        .bg-warning:after {
            content: "⌛" !important;
            position: absolute !important;
            right: 3px !important;
            top: 2px !important;
        }

        .bg-info {
            background-color: white !important;
            border-color: #17a2b8 !important;
            position: relative !important;
        }

        .bg-info:after {
            content: "!" !important;
            position: absolute !important;
            right: 3px !important;
            top: 2px !important;
        }

        .bg-secondary {
            background-color: white !important;
            border-color: #6c757d !important;
            position: relative !important;
        }

        .bg-secondary:after {
            content: "-" !important;
            position: absolute !important;
            right: 3px !important;
            top: 2px !important;
        }

        /* إضافة ترويسة وتذييل للصفحة */
        .print-header, .print-footer {
            display: block !important;
        }

        .d-none.print-header {
            display: block !important;
            text-align: center !important;
            margin-bottom: 20px !important;
            border-bottom: 2px solid #000 !important;
            padding-bottom: 10px !important;
            width: 100% !important;
            position: running(header) !important;
        }

        .d-none.print-footer {
            display: block !important;
            text-align: center !important;
            margin-top: 20px !important;
            border-top: 2px solid #000 !important;
            padding-top: 10px !important;
            font-size: 10pt !important;
            width: 100% !important;
            position: running(footer) !important;
        }

        @page {
            @top-center { content: element(header); }
            @bottom-center { content: element(footer); }
            @bottom-right { content: counter(page) " / " counter(pages); }
        }

        /* إخفاء زر الطباعة نفسه عند الطباعة */
        .btn {
            display: none !important;
        }
    }
</style>
{% endblock %}

{% block content %}
<h2><i class="bi bi-file-earmark-text-fill me-2"></i>تقارير حضور الطلاب</h2>
<hr>

<!-- === نموذج البحث والتصفية === -->
<form method="GET" action="{{ url_for('student_reports') }}" class="mb-4 p-3 border rounded bg-light">
    <div class="row g-3 align-items-end">
        <div class="col-md-3">
            <label for="search_query" class="form-label"><i class="bi bi-search me-1"></i>بحث بالاسم أو المعرف:</label>
            <input type="text" class="form-control form-control-sm" id="search_query" name="search_query" value="{{ request.args.get('search_query', '') }}" placeholder="اكتب اسم الطالب أو المعرف...">
        </div>
        <div class="col-md-3">
            <label for="filter_grade" class="form-label"><i class="bi bi-layers me-1"></i>تصفية حسب الصف:</label>
            <select class="form-select form-select-sm" id="filter_grade" name="filter_grade">
                <option value="">الكل</option>
                {% for grade in unique_grades %}
                <option value="{{ grade }}" {% if request.args.get('filter_grade') == grade %}selected{% endif %}>{{ grade }}</option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-2">
            <label for="filter_classroom" class="form-label"><i class="bi bi-door-open me-1"></i>تصفية حسب الفصل:</label>
            <select class="form-select form-select-sm" id="filter_classroom" name="filter_classroom">
                <option value="">الكل</option>
                {% for classroom in unique_classrooms %}
                <option value="{{ classroom }}" {% if request.args.get('filter_classroom') == classroom %}selected{% endif %}>{{ classroom }}</option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-2">
            <button type="submit" class="btn btn-primary btn-sm w-100"><i class="bi bi-search me-1"></i> بحث/تصفية</button>
        </div>
        <div class="col-md-2">
            <a href="{{ url_for('student_reports') }}" class="btn btn-outline-secondary btn-sm w-100"><i class="bi bi-x-circle me-1"></i> إعادة تعيين</a>
        </div>
    </div>
</form>
<!-- === نهاية نموذج البحث والتصفية === -->


<h4><i class="bi bi-calendar-check me-2"></i>اختيار طالب وتواريخ لعرض التقرير المفصل:</h4>
<!-- === نموذج إنشاء التقرير === -->
<form action="{{ url_for('generate_report') }}" method="POST" class="mb-4 p-3 border rounded bg-light">
    <div class="row g-3 align-items-end">
        <div class="col-md-4">
            <label for="student_id" class="form-label"><i class="bi bi-person-fill me-1"></i>اختر الطالب:</label>
            <select class="form-select" id="student_id" name="student_id" required>
                <option value="" disabled selected>-- اختر طالب --</option>
                {# يتم عرض الطلاب المصفاة هنا #}
                {% for student_id, student in students_to_display.items() %}
                    <option value="{{ student_id }}">{{ student.name }} ({{ student_id }}) - {{ student.grade }}/{{ student.classroom }}</option>
                {% else %}
                    <option value="" disabled>لا يوجد طلاب مطابقون للمعايير</option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-3">
            <label for="start_date" class="form-label"><i class="bi bi-calendar-event me-1"></i>تاريخ البدء:</label>
            <input type="date" class="form-control" id="start_date" name="start_date" required>
        </div>
        <div class="col-md-3">
            <label for="end_date" class="form-label"><i class="bi bi-calendar-event-fill me-1"></i>تاريخ الانتهاء:</label>
            <input type="date" class="form-control" id="end_date" name="end_date" required>
        </div>
        <div class="col-md-2">
            <button type="submit" class="btn btn-success w-100"><i class="bi bi-clipboard-data me-1"></i> إنشاء التقرير</button>
        </div>
    </div>

    <div class="mt-3">
        <div class="alert alert-info">
            <i class="bi bi-info-circle-fill me-2"></i>
            <strong>ملاحظة:</strong> اختر طالبًا وحدد فترة زمنية لإنشاء تقرير مفصل عن حضور الطالب خلال هذه الفترة.
        </div>
    </div>
</form>
<!-- === نهاية نموذج إنشاء التقرير === -->

{% if report_details %}
<div class="print-container">
    <!-- ترويسة الطباعة - تظهر فقط عند الطباعة -->
    <div class="print-header d-none">
        <table style="width: 100%; border-collapse: collapse;">
            <tr>
                <td style="width: 25%; text-align: center; vertical-align: middle;">
                    <img src="{{ url_for('static', filename=settings.logo_path) if settings.logo_path else '' }}" alt="شعار" style="height: 120px; max-width: 100%;">
                </td>
                <td style="width: 50%; text-align: center; vertical-align: middle;">
                    <h2 style="margin-bottom: 10px; font-size: 20pt; font-weight: bold;">المملكة العربية السعودية</h2>
                    <h3 style="margin-bottom: 10px; font-size: 18pt; font-weight: bold;">وزارة التعليم</h3>
                    <h3 style="margin-bottom: 10px; font-size: 16pt; font-weight: bold;">{{ settings.education_department }}</h3>
                    <h3 style="margin-bottom: 5px; font-size: 16pt; font-weight: bold;">{{ settings.school_name }}</h3>
                </td>
                <td style="width: 25%; text-align: center; vertical-align: middle; border: 1px solid #000; padding: 10px; border-radius: 5px;">
                    <p style="margin-bottom: 8px; font-weight: bold;">التاريخ: {{ now().strftime('%Y-%m-%d') }}</p>
                    <p style="margin-bottom: 8px; font-weight: bold;">العام الدراسي: {{ settings.academic_year }}</p>
                    <p style="margin-bottom: 8px; font-weight: bold;">{{ settings.semester }}</p>
                </td>
            </tr>
        </table>
        <h2 style="text-align: center; margin-top: 20px; border-top: 2px solid #000; border-bottom: 2px solid #000; padding: 10px 0; font-size: 22pt; font-weight: bold; color: #000;">تقرير حضور وغياب الطلاب</h2>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="bi bi-person-badge-fill me-2"></i>تقرير حضور الطالب: {{ report_details.student.name }}</h5>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-12">
                    <table class="table table-bordered" style="margin-bottom: 20px;">
                        <tr>
                            <td style="width: 25%; background-color: #f8f9fa; font-weight: bold;">اسم الطالب:</td>
                            <td style="width: 25%;">{{ report_details.student.name }}</td>
                            <td style="width: 25%; background-color: #f8f9fa; font-weight: bold;">المعرف:</td>
                            <td style="width: 25%;">{{ report_details.student_id }}</td>
                        </tr>
                        <tr>
                            <td style="background-color: #f8f9fa; font-weight: bold;">الصف:</td>
                            <td>{{ report_details.student.grade }}</td>
                            <td style="background-color: #f8f9fa; font-weight: bold;">الفصل:</td>
                            <td>{{ report_details.student.classroom }}</td>
                        </tr>
                        <tr>
                            <td style="background-color: #f8f9fa; font-weight: bold;">الفترة:</td>
                            <td colspan="3">من {{ report_details.start_date }} إلى {{ report_details.end_date }}</td>
                        </tr>
                    </table>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-md-12">
                    <table class="table table-bordered">
                        <tr style="background-color: #f8f9fa; font-weight: bold; text-align: center;">
                            <td>إجمالي أيام الحضور</td>
                            <td>إجمالي أيام الغياب</td>
                            <td>إجمالي أيام التأخر</td>
                            <td>إجمالي أيام الغياب بعذر</td>
                            <td>إجمالي الأيام المسجلة</td>
                            <td>نسبة الحضور</td>
                        </tr>
                        <tr style="text-align: center;">
                            <td>{{ report_details.total_present }}</td>
                            <td>{{ report_details.total_absent }}</td>
                            <td>{{ report_details.total_late }}</td>
                            <td>{{ report_details.total_excused }}</td>
                            <td>{{ report_details.total_days }}</td>
                            <td>{{ "%.2f"|format(report_details.attendance_percentage) }}%</td>
                        </tr>
                    </table>
                </div>
            </div>

            <h5 class="mb-3"><i class="bi bi-calendar3 me-2"></i>تفاصيل الحضور اليومية:</h5>
            <div class="table-responsive">
                <table class="table table-striped table-hover border">
                    <thead class="table-dark">
                        <tr>
                            <th>التاريخ</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for day in report_details.report_data %}
                        <tr>
                            <td>{{ day.date }}</td>
                            <td>
                                {% if day.status == 'present' %}
                                    <span class="badge bg-success">حاضر</span>
                                {% elif day.status == 'absent' %}
                                    <span class="badge bg-danger">غائب</span>
                                {% elif day.status == 'late' %}
                                    <span class="badge bg-warning text-dark">متأخر</span>
                                {% elif day.status == 'excused' %}
                                    <span class="badge bg-info text-dark">غائب بعذر</span>
                                {% else %}
                                    <span class="badge bg-secondary">لم يسجل</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- ملخص الحضور في نهاية التقرير -->
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="alert alert-light border">
                        <h6 class="mb-3">ملخص الحضور:</h6>
                        <div class="row text-center">
                            <div class="col">
                                <div class="p-2 border rounded">
                                    <span class="badge bg-success">حاضر</span>
                                    <h4>{{ report_details.total_present }}</h4>
                                </div>
                            </div>
                            <div class="col">
                                <div class="p-2 border rounded">
                                    <span class="badge bg-danger">غائب</span>
                                    <h4>{{ report_details.total_absent }}</h4>
                                </div>
                            </div>
                            <div class="col">
                                <div class="p-2 border rounded">
                                    <span class="badge bg-warning text-dark">متأخر</span>
                                    <h4>{{ report_details.total_late }}</h4>
                                </div>
                            </div>
                            <div class="col">
                                <div class="p-2 border rounded">
                                    <span class="badge bg-info text-dark">غائب بعذر</span>
                                    <h4>{{ report_details.total_excused }}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تذييل الطباعة - يظهر فقط عند الطباعة -->
            <div class="print-footer d-none">
                <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                    <tr>
                        <td style="width: 33%; text-align: center; padding: 10px;">
                            <p style="margin-bottom: 5px; font-weight: bold; font-size: 14pt;">توقيع المعلم</p>
                            <div style="border-bottom: 1px solid #000; height: 40px;"></div>
                            <p style="margin-top: 5px;">الاسم: ...................................</p>
                        </td>
                        <td style="width: 33%; text-align: center; padding: 10px;">
                            <p style="margin-bottom: 5px; font-weight: bold; font-size: 14pt;">توقيع المرشد الطلابي</p>
                            <div style="border-bottom: 1px solid #000; height: 40px;"></div>
                            <p style="margin-top: 5px;">الاسم: ...................................</p>
                        </td>
                        <td style="width: 33%; text-align: center; padding: 10px;">
                            <p style="margin-bottom: 5px; font-weight: bold; font-size: 14pt;">توقيع قائد المدرسة</p>
                            <div style="border-bottom: 1px solid #000; height: 40px;"></div>
                            <p style="margin-top: 5px;">الاسم: ...................................</p>
                        </td>
                    </tr>
                </table>
                <div style="text-align: center; margin-top: 20px; border-top: 1px solid #000; padding-top: 10px;">
                    <p style="font-size: 10pt;">تم إنشاء هذا التقرير بواسطة نظام إدارة المدرسة - {{ settings.school_name }} - {{ now().strftime('%Y-%m-%d') }}</p>
                </div>
            </div>

            <div class="mt-3 no-print">
                <div class="row">
                    <div class="col-md-6 d-grid gap-2 d-md-flex justify-content-md-start">
                        <button class="btn btn-success" onclick="window.print()"><i class="bi bi-printer-fill me-1"></i> طباعة التقرير</button>
                        <a href="{{ url_for('student_reports') }}" class="btn btn-secondary"><i class="bi bi-arrow-left me-1"></i> العودة</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

{% endblock %}

{% block scripts %}
{{ super() }}
{% endblock %}